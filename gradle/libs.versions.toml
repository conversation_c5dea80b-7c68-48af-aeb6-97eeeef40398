[versions]
kotlin_version = "2.0.21"
ksp_version = "2.0.21-1.0.28"
binary_validator_version = "0.16.2"
ktlint_version = "12.1.1"
kover_version = "0.8.0"
detekt_version = "1.22.0"
vanniktech_version = "0.27.0"
agp_version = "7.4.2"
panther_gradle_version = "26864.0"
panther_xml_version = "1.1"
atomic_fu_version = "0.25.0"
spotbugs_version = "5.0.13"
mockk_version = "1.13.11"
brazil_cli_version = "1.0.21"
junit_version = "5.9.2"
gradle_buildconfig = "5.5.1"
kotlin_inject_version = "0.7.0-3340303"
# This is temporary. Ideally, exports should pick up
guice = "4.1.0"
app-platform = "0.1.4.1"

[libraries]
kover = { module = "org.jetbrains.kotlinx:kover-gradle-plugin", version.ref = "kover_version" }
ktlint = { module = "org.jlleitschuh.gradle:ktlint-gradle", version.ref = "ktlint_version" }
ksp-gradle-plugin = { module = "com.google.devtools.ksp:symbol-processing-gradle-plugin", version.ref = "ksp_version" }
kotlin-gradle-plugin = { module = "org.jetbrains.kotlin:kotlin-gradle-plugin", version.ref = "kotlin_version" }
kotlin-binary-validator = { module = "org.jetbrains.kotlinx:binary-compatibility-validator", version.ref = "binary_validator_version" }
detekt = { module = "io.gitlab.arturbosch.detekt:detekt-gradle-plugin", version.ref = "detekt_version" }
vanniktech = { module = "com.vanniktech.maven.publish:com.vanniktech.maven.publish.gradle.plugin", version.ref = "vanniktech_version" }
android-app = { module = "com.android.application:com.android.application.gradle.plugin", version.ref = "agp_version" }
android-lib = { module = "com.android.library:com.android.library.gradle.plugin", version.ref = "agp_version" }
panther-gradle-plugin = { module = "com.amazon.lastmile.brazil.internal:PantherAndroidGradlePlugin-1.0", version.ref = "panther_gradle_version" }
panther-xml-handler = { module = "com.amazon.gradle.android:PantherAndroidXmlTagHandler", version.ref = "panther_xml_version" }
spotbugs-gradle-plugin = { module = "com.github.spotbugs.snom:spotbugs-gradle-plugin", version.ref = "spotbugs_version" }
atomic-fu-gradle-plugin = { module = "org.jetbrains.kotlinx:atomicfu-gradle-plugin", version.ref = "atomic_fu_version" }
mockk = { module = "io.mockk:mockk", version.ref = "mockk_version" }
kotlinx-serialization = { module = "org.jetbrains.kotlin:kotlin-serialization", version.ref = "kotlin_version" }
peru-brazil-cli = { module = "software.amazon.peru:brazilcli", version.ref = "brazil_cli_version" }
junit-jupiter = { module = "org.junit.jupiter:junit-jupiter", version.ref = "junit_version" }
# Kotlin Inject Libraries
kotlin-inject-compiler = { module = "com.amazon.lastmile.fork.me.tatarka.inject:kotlin-inject-compiler-ksp", version.ref = "kotlin_inject_version" }
kotlin-inject-runtime = { module = "com.amazon.lastmile.fork.me.tatarka.inject:kotlin-inject-runtime", version.ref = "kotlin_inject_version" }
google-guice = { module = "com.google.inject:guice", version.ref = "guice" }
gradle-build-config = { module = "com.github.gmazzo.buildconfig:plugin", version.ref = "gradle_buildconfig" }
app-platform-gradle-plugin = { module = "com.amazon.lastmile.app.platform.k2:gradle-plugin", version.ref = "app-platform" }

[plugins]
ktlint = { id = "org.jlleitschuh.gradle.ktlint", version.ref = "ktlint_version" }
kotlin-jvm = { id = "org.jetbrains.kotlin.jvm", version.ref = "kotlin_version" }
detekt = { id = "io.gitlab.arturbosch.detekt", version.ref = "detekt_version" }
gradle-buildconfig = { id = "com.github.gmazzo.buildconfig", version.ref = "gradle_buildconfig" }

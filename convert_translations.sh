#!/bin/bash

# Convert Panther puff.json files to XML string resources
# This script only runs the conversion step (no panther translate)
# Usage: ./convert_translations.sh

set -e  # Exit on any error

echo "🔄 Converting Panther translations to XML..."

# Check if we're in the right directory
if [ ! -f "build.gradle" ] || [ ! -f "convert_puff_to_xml.py" ]; then
    echo "❌ Error: This script must be run from the MapsEngagementAndroidSDK root directory"
    echo "   Current directory: $(pwd)"
    echo "   Expected files: build.gradle, convert_puff_to_xml.py"
    exit 1
fi

# Run the conversion script
if python3 convert_puff_to_xml.py; then
    echo "✅ XML conversion completed successfully!"
else
    echo "❌ XML conversion failed"
    exit 1
fi

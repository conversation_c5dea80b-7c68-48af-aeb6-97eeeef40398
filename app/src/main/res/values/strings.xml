<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<resources>
    <string name="rabbit_app_name">Amazon Mobile Delivery</string>
    <string name="login_successful">Sign in successful</string>
    <string name="title_activity_login">Amazon Flex Sign In</string>
    <string name="drawer_toggle_content_desc">Open/close menu</string>
    <string name="rabbit_transporter_phone_country_code">US</string>
    <string name="and">and</string>
    <string name="accept">Accept</string>
    <string name="agree">Agree</string>
    <string name="yes">Yes</string>
    <string name="no">No</string>
    <string name="retry">Retry</string>
    <string name="done">Done</string>
    <string name="close">Close</string>
    <string name="not_applicable">N/A</string>
    <string name="username">Your Amazon ID</string>
    <string name="password">Password</string>
    <string name="cannot_sign_in">If you can\\\'t sign in, call Support.</string>
    <string name="call_manager_for_assistance">Call Support for assistance.</string>
    <string name="sign_in">Sign in</string>
    <string name="inactive_logout">Sign in as another user</string>
    <string name="sign_in_with_amazon">Sign in with Amazon</string>
    <string name="prompt_email">username\@amazon.com</string>
    <string name="action_sign_in_short">Sign in</string>
    <string name="error_invalid_email">This email address is invalid</string>
    <string name="error_invalid_password">This password is too short</string>
    <string name="error_not_valid_transporter">You are not registered to make deliveries.</string>
    <string name="error_reauthenticate_mis_match">Please sign in using the current user\\\'s credentials</string>
    <string name="logout">Sign out</string>
    <string name="unable_to_logout">Unable to sign out</string>
    <string name="logout_in_progress">Signing out</string>
    <string name="please_wait">Please wait…</string>
    <string name="signing_in">Signing in</string>
    <string name="info_security_log_out">For security purposes, you have been automatically signed out.</string>
    <string name="login_eula_text">By signing in, you agree to the application\n%1$s.</string>
    <string name="login_eula_text_clickable">terms and conditions</string>
    <string name="login_username_hint">Username or email address</string>
    <string name="login_password_hint">Password</string>
    <string name="login_forgot_password">Forgot password</string>
    <string name="login_show_password">Show password</string>
    <string name="login_sign_in_instruction">Sign in with your\nAmazon.com account</string>
    <string name="login_version_info">Version %1$s</string>
    <string name="login_training_flavor">For training use only.</string>
    <string name="go_to_register">Create an account</string>
    <string name="notification_login_network_error">Unable to login due to a network error. Please try again later.</string>
    <string name="compatibility_dialog_title">Upgrade required</string>
    <string name="device_not_supported_message">The Amazon Flex app requires the following to run:</string>
    <string name="device_not_supported_minimum_os">• Android version 4.4 or above</string>
    <string name="device_not_supported_remedy">To continue delivering, you\\\'ll need to upgrade your device\\\'s software or get a new device.</string>
    <string name="clear_app_cache_success">Cache cleared successfully.</string>
    <string name="clear_app_cache_failure">Could not clear cache. Please try again.</string>
    <string name="unable_to_retrieve_blocks">Sorry, we\\\'ve encountered a problem retrieving your scheduled blocks. Please try restarting the app.</string>
    <string name="bluetooth_disabled_alert_title">Bluetooth Disabled</string>
    <string name="bluetooth_disabled_alert_content">In order for the Amazon Flex app to work correctly, you must enable Bluetooth to continue delivering</string>
    <string name="bluetooth_disabled_alert_body">You must enable Bluetooth to continue delivering</string>
    <string name="bluetooth_disabled_alert_button">Go to device settings</string>
    <string name="device_model_make_not_eligibility_blocking_title">This device is no longer supported.</string>
    <string name="device_model_make_not_eligibility_blocking_reason">The Amazon Flex app no longer runs on Android %1$s %2$s.</string>
    <string name="device_model_make_not_eligibility_blocking_solution">To continue delivering, upgrade to a new Android device with 2GB RAM or more.</string>
    <string name="device_model_make_not_eligibility_warning_title">Upgrade to a new device.</string>
    <string name="device_model_make_not_eligibility_warning_reason">To continue delivering, upgrade to a new Android device with 2GB RAM or more.</string>
    <string name="device_model_make_not_eligibility_warning_solution">Beginning %1$s, the Amazon Flex app will no longer run on Android %2$s %3$s.</string>
    <string name="device_ram_not_eligibility_blocking_title">This device is no longer supported.</string>
    <string name="device_ram_not_eligibility_blocking_reason">The Amazon Flex app no longer runs on Android devices that have less than 2GB of available RAM.</string>
    <string name="device_ram_not_eligibility_blocking_solution">To continue delivering, upgrade to a new Android device with 2GB RAM or more.</string>
    <string name="device_ram_not_eligibility_warning_title">Upgrade to a new device with more available memory.</string>
    <string name="device_ram_not_eligibility_warning_reason">To continue delivering, upgrade to a new Android device with 2GB RAM or more.</string>
    <string name="device_ram_not_eligibility_warning_solution">Beginning %1$s, the Amazon Flex app will no longer run on Android devices that have less than 2GB of available RAM</string>
    <string name="device_os_not_eligibility_blocking_title">Update to Android %1$s or newer.</string>
    <string name="device_os_not_eligibility_blocking_reason">The Amazon Flex app no longer supports devices running Android versions below %1$s.</string>
    <string name="device_os_not_eligibility_blocking_solution">To continue delivering, please update your device to Android version %1$s or above.</string>
    <string name="device_os_not_eligibility_warning_title">Update to Android %1$s or newer.</string>
    <string name="device_os_not_eligibility_warning_reason">Beginning %1$s, the Amazon Flex app will no longer support devices running Android versions below %2$s.</string>
    <string name="device_os_not_eligibility_warning_solution">To continue delivering after %1$s, please update your device to Android version %2$s or above.</string>
    <string name="device_not_compatible_title">Upgrade your device</string>
    <string name="device_not_compatible_body">Your device doesn\\\'t meet the minimum requirements. To continue delivering you\\\'ll need a device that meets these requirements:</string>
    <string name="device_not_compatible_requirements">Android OS version 4.4 or later \n\nAt least 2GB of memory</string>
    <string name="update_required_title">Upgrade to the new app version</string>
    <string name="update_required_body">You need to install the latest version now before you can continue delivering.</string>
    <string name="update_recommended_body_soon">To continue delivering, you\\\'ll need to install the latest version soon.</string>
    <string name="update_recommended_body">To continue delivering, you\\\'ll need to install the latest version by %s.</string>
    <string name="update_button_install">Install Now</string>
    <string name="update_button_remind_later">Remind Me Later</string>
    <string name="update_installation_title">Starting Installation</string>
    <string name="update_installation_body">The app will quit shortly to complete installation.\n\nIf you accidentally selected \"Cancel\", tap below to retry installation.</string>
    <string name="update_install_button_retry">Retry Installation</string>
    <string name="update_download_in_progress">Download in progress</string>
    <string name="update_download_instructions">The installation will begin shortly.</string>
    <string name="update_download_failed_title">Update download failed</string>
    <string name="update_download_failed_body">Re-download the update to get the latest version. Ensure that you have an internet connection.</string>
    <string name="update_download_failed_insufficient_disk_space_body">Insufficient disk space to download update. Please clear space on the device and then re-download the update.</string>
    <string name="update_check_failed_title">Unable to check for updates</string>
    <string name="update_check_failed_body">Connect to the internet and tap &lt;b&gt;TRY AGAIN&lt;/b&gt;.</string>
    <string name="stop_list_completed">Completed</string>
    <string name="travel_parked_at_stop">I\\\'ve parked</string>
    <string name="travel_arrive_check_in">Check in</string>
    <string name="travel_arrive_try_check_in_again">Try check in again</string>
    <string name="travel_arrive_check_in_header">Check in after %1$s</string>
    <string name="travel_arrive_check_in_try_again_description">You can check in for your scheduled pickup between %1$s and %2$s\n\nTap below to try again.</string>
    <string name="travel_arrive_check_in_description">You can check in for your scheduled pickup between %1$s and %2$s.</string>
    <string name="travel_arrive_checkin_progress">Checking in…</string>
    <string name="travel_deliver_text_deliver_before">Deliver by</string>
    <string name="travel_options_secure_delivery">Attempt Secure Delivery</string>
    <string name="checkin_title_activity">Check in</string>
    <string name="check_out">Check out</string>
    <string name="travel_check_out_description">Once you’ve finished working, swipe below to check out.</string>
    <string name="missed_punch_toast">You’ve been automatically checked out of your last route since you are finished.</string>
    <string name="lockers_we_missed_you_instructions">Leave a We Missed You card with the &lt;b&gt;Amazon Locker&lt;/b&gt; option selected.\n\nYou\\\'ll deliver this package to an Amazon Locker later.</string>
    <string name="lockers_we_missed_you_done_button">\@string/done</string>
    <string name="lockers_itinerary_change_dialog_positive">\@string/ok</string>
    <string name="lockers_skip_to_stop_dialog_title">Skip to This Stop\?</string>
    <string name="lockers_skip_to_stop_dialog_positive">Skip to stop</string>
    <string name="lockers_skip_to_stop_dialog_negative">Don\\\'t skip</string>
    <string name="lockers_finishing_pickup_text">Finishing Pickup</string>
    <string name="tr_list_customer_name_label">Package for:</string>
    <string name="lockers_pickup_options_unable_to_pickup_header">Unable to pick up at Locker</string>
    <string name="lockers_pickup_options_unable_to_pickup_text">Mark order as unavailable and continue with other orders</string>
    <string name="divert_reason_cancelled">No longer required</string>
    <string name="task_rabbit_notification_center">Notification center</string>
    <string name="task_rabbit_notification_center_deadline_label">&lt;b&gt;Complete by: &lt;/b&gt;</string>
    <string name="task_rabbit_notification_center_deadline_immediate">IMMEDIATELY</string>
    <string name="task_rabbit_notification_center_empty">You have no new notifications that require action.</string>
    <string name="take_selfie_directions_title">Instructions</string>
    <string name="take_selfie_tips_title">Tips</string>
    <string name="take_selfie_take_photo">Take Photo</string>
    <string name="take_selfie_retake">Retake</string>
    <string name="take_selfie_submit">Use Photo</string>
    <string name="take_selfie_eyes_visible">Eyes not visible</string>
    <string name="take_selfie_face_visible">Face not found</string>
    <string name="take_selfie_mouth_visible">Mouth not visible</string>
    <string name="take_selfie_poor_lighting">Poor lighting</string>
    <string name="take_selfie_multiple_faces">Multiple faces</string>
    <string name="take_selfie_use_this_photo">Do you want to use this photo\?</string>
    <string name="take_selfie_check_tips">Before you continue, double check that you\\\'ve followed the tips below.</string>
    <string name="take_selfie_adjustment_needed">Adjustment needed</string>
    <string name="take_selfie_adjustment_eyes_visible">Check that both eyes are open and visible</string>
    <string name="take_selfie_adjustment_mouth_visible">Confirm your mouth is in the picture</string>
    <string name="take_selfie_adjustment_face_visible">Make sure there\\\'s a single face in the photo</string>
    <string name="take_selfie_issues">Issues\?</string>
    <string name="take_selfie_call_support">Call support</string>
    <string name="take_selfie_camera_permission_needed">The Amazon Flex App was unable to access the device\\\'s camera. Go to the App\\\'s Settings and check that it has permission to use the camera.</string>
    <string name="take_selfie_completed">Thanks. We\\\'ll let you know if there\\\'s a problem.</string>
    <string name="take_selfie_confirm_error">Photo quality is poor. Please try again.</string>
    <string name="first_locker_pickup_title">Get ready for your first pickup at a Locker</string>
    <string name="first_locker_pickup_message">Up next, you\\\'ll be picking up packages at a Locker.\n\nCheck the number of packages and bring a bag or dolly if needed.</string>
    <string name="first_time_dialog_got_it">Got it</string>
    <string name="first_store_pickup_title">Get ready for your first pickup at a Store</string>
    <string name="first_store_pickup_message">Up next, you’ll be picking up packages at a Store.\n\nCheck the number of packages and bring a bag or dolly if needed.</string>
    <string name="first_return_empty_package_title">Return empty Amazon packaging</string>
    <string name="first_return_empty_package_message">Return empty packaging the same way you\\\'d return undelivered package. Hand it to a station associate. They will confirm they received it.</string>
    <string name="store_pickup_instructions"> • Go inside and find packages for pickup\n• Ask a store associate if you don’t see anything\n• Scan each package</string>
    <string name="store_pickup_local_rush_retail_instructions">Follow the instructions below, then find a store associate to get access to packages.</string>
    <string name="store_pickup_local_rush_retail_instructions_no_address_info">Find a store associate to get access to packages</string>
    <string name="first_fsm_pickup_get_ready_title">Get ready for your first shop and deliver order</string>
    <string name="first_fsm_pickup_get_ready_message">Go inside the store and launch the Shopping app from the Amazon Flex app. Sign-in with your Amazon Flex account.</string>
    <string name="first_fsm_pickup_shop_title">Shop for your order</string>
    <string name="first_fsm_pickup_shop_message">You\\\'ll be assigned an order when you log-in. Shop for the items listed on the order, bag and scan them.</string>
    <string name="first_fsm_pickup_deliver_title">Deliver your order</string>
    <string name="first_fsm_pickup_deliver_message">When you\\\'re done, the Shopping app will prompt you to come back to the Amazon Flex app to deliver the order.</string>
    <string name="first_time_dialog_next">Next</string>
    <string name="sort_assist_ftux_title_first_slide">New package labels</string>
    <string name="sort_assist_ftux_message_first_slide">Our new 4-digit numbers are sequential. You can use them to organize packages along your route.</string>
    <string name="sort_assist_ftux_title_second_slide">Seeing letters\?</string>
    <string name="sort_assist_ftux_message_second_slide">When a package doesn\\\'t have a delivery sequence, we\\\'ll show a common word or phrase instead.</string>
    <string name="undeliverable_call_dispatcher_instructions">Tap below to call &lt;b&gt;Support&lt;/b&gt;.</string>
    <string name="post_call_dispatcher_title">Did you reach Support\?</string>
    <string name="post_call_dispatcher_yes">Yes</string>
    <string name="post_call_dispatcher_no">No</string>
    <string name="call_dispatcher_again_button">Call Again</string>
    <string name="dispatcher_recommendation_title">What did Support recommend\?</string>
    <string name="deliver_recommendation">Deliver the package</string>
    <string name="dont_deliver_recommendation">Don\\\'t deliver the package</string>
    <string name="dispatcher_instructions_title">Support Instructions</string>
    <string name="help_options_call_customer_no_suffix">Call %1$s</string>
    <string name="help_options_call_customer">Call %1$s, the customer</string>
    <string name="help_options_call_shipper">Call %1$s, the shipper</string>
    <string name="help_options_text_customer">Text %1$s, the customer</string>
    <string name="help_options_text_customers">Text Customer</string>
    <string name="help_options_text_customer_opt_out">Customer cannot receive texts</string>
    <string name="help_options_text_shipper">Text %1$s, the shipper</string>
    <string name="select_customer">Select customer</string>
    <string name="help_options_call_customers">Call Customer</string>
    <string name="help_options_call_merchant">Call %1$s</string>
    <string name="help_options_faq">Frequently Asked Questions</string>
    <string name="help_options_return_to_station">Return to Station</string>
    <string name="help_options_unable_to_take_photo">Unable to take photo</string>
    <string name="faqs">FAQs</string>
    <string name="healthcare_subsidy_card_title">Healthcare Subsidy</string>
    <string name="healthcare_subsidy_card_text">Learn more and see if you qualify.</string>
    <string name="help_options_return_items_header">Return items</string>
    <string name="help_options_return_items_text">Remove items that are damaged or that the customer doesn\\\'t want.</string>
    <string name="help_options_return_items_locker_text">Remove items that are damaged, missing or can\\\'t be recognized by the Locker.</string>
    <string name="help_options_undeliverable_header">Unable to deliver</string>
    <string name="help_options_undeliverable_text">You\\\'ll deliver to other locations at later stops or return packages to the station.</string>
    <string name="help_options_undeliverable_locker_text">Mark this order as undeliverable. You\\\'ll return all packages to the station.</string>
    <string name="help_options_report_safety_concern">Report a safety concern</string>
    <string name="help_options_report_dog_on_route">Report a dog on your route</string>
    <string name="help_options_end_block">End block</string>
    <string name="help_options_end_block_description">For urgent change in plans, navigate back to the station and return undelivered packages.</string>
    <string name="help_options_unable_to_take_photo_text">If you\\\'re having camera issues, or can\\\'t take a photo for some other reason.</string>
    <string documentation="End Block Confirmation Dialog Title" name="end_block_confirmation_dialog_title_text">Are you sure you want to end your block\?</string>
    <string documentation="End Block Confirmation Dialog Body Text" name="end_block_confirmation_dialog_body_text">Ending your block will require you to return all undelivered packages back to the station. This may impact your standings.</string>
    <string documentation="End Block Confirmation Dialog Primary Button Text" name="end_block_confirmation_dialog_primary_button_text">Contact Support for help</string>
    <string documentation="End Block Confirmation Dialog Secondary Button Text" name="end_block_confirmation_dialog_secondary_button_text">End block</string>
    <string name="return_reason_main_text_items_damaged">Rejected - Item Damage</string>
    <string name="return_reason_items_damaged_title">Which items are damaged\?</string>
    <string name="call_dispatcher_recommendation_title">What did Support recommend\?</string>
    <string name="call_dispatcher_recommendation_deliver">Deliver the package</string>
    <string name="call_dispatcher_recommendation_nodeliver">Don\\\'t deliver now</string>
    <string name="pickup_stop_options_unable_to_pickup_header">Unable to pick up</string>
    <string name="pickup_stop_options_unable_to_pickup_text">Mark all orders at this stop as unavailable. You\\\'ll continue with other orders or return to the station.</string>
    <string name="help_options_problem_with_package_header">Problem with package</string>
    <string name="help_options_problem_with_package_text">Leave behind packages that aren\\\'t ready for pickup.</string>
    <string name="help_options_skip_stop_header">Skip this stop</string>
    <string name="help_options_skip_stop_text">Report this pickup as attempted without picking up. You\\\'ll be able to come back later if you have time.</string>
    <string name="help_options_pickup_call_support">Request call from support</string>
    <string name="help_options_pickup_issue_scheduled_package">Issue with a scheduled package</string>
    <string name="help_options_pickup_issue_picking_up">Issue picking up at this merchant</string>
    <string name="help_options_switch_vehicle_header">Vehicle is deemed unsafe by Amazon Auditor</string>
    <string name="help_options_switch_vehicle_text">Re-inspect the unsafe vehicle and report the issues.</string>
    <string name="wait_title_activity">PICK UP</string>
    <string name="wait_headline_wait">Wait for order</string>
    <string name="wait_headline_continue">Continue pickup</string>
    <string name="wait_problem">Problem</string>
    <string name="wait_delivery_details">Delivery details</string>
    <string name="wait_order_being_prepared">The order is being prepared.</string>
    <string name="wait_order_continue">Continue</string>
    <string name="wait_order_ready">The order is ready for you to pick up</string>
    <string name="wait_order_scan">You\\\'ll scan packages in the next step.</string>
    <string name="wait_for_work_please_standby">Please stand by</string>
    <string name="wait_for_work_detailed_text">Wait in the area until you are assigned more deliveries.</string>
    <string name="wait_for_work_swipe_to_go_off_duty_button">Swipe to go off duty</string>
    <string name="wait_for_work_manual_pickup_button">Pick up packages manually</string>
    <string name="work_for_work_title">WAIT</string>
    <string name="wait_for_work_waypoint_message_title">Wait in this area</string>
    <string name="wait_for_work_waypoint_message">Wait in the area until you are assigned more deliveries.</string>
    <string name="wait_for_work_waypoint_missing_address">There was a problem loading the address. Please call support for assistance.</string>
    <string name="pickup_help_menu_instructions_title">Information</string>
    <string name="pickup_help_menu_instructions_content">Aim your device at each label from 1–2 feet away. Too dark\? Try the flashlight. Still won\\\'t scan\? Go to Help to enter manually.</string>
    <string name="pickup_help_menu_instructions_content_undo">To leave packages you picked up, select one or more orders and confirm your selection.</string>
    <string name="station_pickup_qrcode_check_in_button">\@string/scan_arrival_code</string>
    <string name="station_pickup_instructions_title">What to do next:</string>
    <string name="station_pickup_instructions_content"> • Park near entrance or drive up to loading dock\n• Pick up route assignment from station operator\n• Scan each package and load in vehicle before departing</string>
    <string name="station_pickup_instructions_prime_now_content"> • Park near entrance and remain in the waiting area for orders to be assigned.\n• Your phone will buzz when the orders are assigned to you. Click “Acknowledge and Begin” to start pickup.\n• Follow the instructions in the app to complete pickup.</string>
    <string name="pickup_store_local_rush_retail_instructions_title">Once you\\\'re at the store:</string>
    <string name="pickup_store_instructions_title">How to pick up from a store:</string>
    <string name="pickup_store_instructions_message"> • If the order isn\\\'t ready, wait outside\n• If the order is ready, go inside and find your assigned packages\n• Find and scan each of your assigned packages\n• Not Park in Designated Customer Parking</string>
    <string name="exchange_instructions_title">How to exchange an item:</string>
    <string name="exchange_instructions_message"> • Verify the returned item matches exchange details\n • Pack the returned item and apply a label\n • Scan the returned package barcode</string>
    <string name="exchange_verify_title">Verify the returned item matches the following details</string>
    <string name="exchange_collect_item_title">Collect item upon delivery</string>
    <string name="exchange_verify_instruction_index">-</string>
    <string name="pickup_nonscannable_instructions_title">Pickup Instructions:</string>
    <string name="pickup_nonscannable_instructions_message">1. Enter the store or restaurant and ask for the order by mentioning the Order ID. To see the Order ID, click Continue.\n2. Check if you have the right item(s) by matching the Order ID on the package with the Order ID in your device.\n3. For restaurant orders, pack hot and cold items separately.</string>
    <string name="pickup_customer_return_instructions_title">How to pick up from a customer:</string>
    <string name="pickup_customer_return_instructions_message"> • Meet the customer in person\n• Attach a label to each package\n• Scan each package</string>
    <string name="pickup_customer_return_amxl_instructions_message"> • Call/text customer before arriving\n• Meet the customer in person to retrieve package\n• Attach label to package and scan</string>
    <string name="pickup_customer_return_amxl_unattended_instructions_message"> • Call/text customer before arriving\n• Retrieve package from customer or from customer threshold\n• Attach label to package and scan</string>
    <string name="pickup_mfn_instructions_message"> • Find a company representative\n • Find and scan each assigned package</string>
    <string name="pickup_wait_title">WAIT</string>
    <string name="pickup_wait_outside_title">Wait outside</string>
    <string name="pickup_wait_outside_message">You\\\'ll be notified when your packages are ready to pick up.</string>
    <string name="pickup_go_inside_title">Go inside</string>
    <string name="pickup_go_inside_message">Your packages are ready to pick up.</string>
    <string name="pickupEquipment">Pick up equipment</string>
    <string name="pickupEquipmentHeader">We recommend taking a vest, gloves and a box cutter on this route</string>
    <string name="pickupEquipmentBody">You\\\'ll have to remove the packaging for a delivery on your route. If you don\\\'t have any of this equipment, ask a station associate for it.\n\nWait for the app to prompt you to remove the packaging.</string>
    <string name="pickupEquipmentButton">\@string/gotIt</string>
    <string name="selection_row_title">Package barcode</string>
    <string name="missing_package_title">Package missing</string>
    <string name="missing_package_subheader">Select missing packages</string>
    <string name="missing_package_finish_button_text">Remove</string>
    <string name="package_scannable_id_row_title">Barcode</string>
    <string name="container_row_title">Container</string>
    <string name="pickup_exception_row_title">Pickup exception</string>
    <string name="selection_continue_picking_up">Continue picking up</string>
    <string name="selection_continue">Continue</string>
    <string name="search_hint">Search by tracking ID</string>
    <string name="help_option_missing_package">Package is missing</string>
    <string name="cube_out_package_title">Package can\\\'t fit</string>
    <string name="cube_out_package_subheader">Scan packages that can\\\'t fit into your vehicle</string>
    <string name="help_option_cube_out">Package can\\\'t fit into vehicle</string>
    <string name="cube_out_package_finish_button_text">Remove</string>
    <string name="scan_cancellation_title">Cancel delivery\?</string>
    <string name="scan_cancellation_text_1">If you cancel your delivery, all items will be removed.</string>
    <string name="scan_cancellation_text_2">Do you want to cancel your delivery\?</string>
    <string name="scan_button_yes">Yes, cancel delivery</string>
    <string name="scan_button_no">No, continue delivery</string>
    <string name="package_exception_title">Approval required</string>
    <string name="package_exception_message">Show your device to an Amazon associate for approval.</string>
    <string name="package_exception_transporter_title">Transporter ID:</string>
    <string name="package_exception_button_text">Cancel</string>
    <string name="package_exception_missing">Missing</string>
    <string name="package_exception_cube_out">Does not fit</string>
    <string name="pickup_selection_not_ready_package_subheader">Which package(s) are missing or not ready\?</string>
    <string name="pickup_selection_poor_packaging_subheader">Which package(s) need to be repacked securely\?</string>
    <string name="pickup_selection_failed_verification_subheader">Which package(s) couldn’t be verified\?</string>
    <string name="call_button_text">Call %1$s</string>
    <string name="report_pickup_problem_button_text">Report pickup problem</string>
    <string name="pickup_signature_name">Customer</string>
    <string name="travel_options_manual_show_ive_arrived">Show I\\\'ve Arrived</string>
    <string name="travel_options_title">What\\\'s the problem\?</string>
    <string name="travel_options_customer_return_header">Return items</string>
    <string name="travel_options_customer_return_text">Omit items that are damaged or the customer doesn\\\'t want.</string>
    <string name="travel_options_customer_unable_to_deliver_header">Unable to deliver</string>
    <string name="travel_options_customer_unable_to_deliver_text">Skip this delivery and bring all items back to the station.</string>
    <string name="travel_options_customer_call_header">Call customer: %1$s</string>
    <string name="travel_options_customer_call_text">Call the customer if you\\\'re going to be late</string>
    <string name="pickup_instructions_overlay_text_unified_pickup">Pick up all available packages</string>
    <string name="schedule_a_new_time">Schedule a new time</string>
    <string name="pickup_rescheduled">Pickup rescheduled</string>
    <string name="reschedule_pickup_header">When would the customer like pickup\?</string>
    <string name="cancel_new_pickup_time">Cancel new pickup time</string>
    <string name="reschedule">RESCHEDULE</string>
    <string name="tomorrow">Tomorrow</string>
    <string name="stop_arrival_activity_title">STOP ARRIVAL</string>
    <string name="scan_packages_button_text">Scan packages</string>
    <string name="manual_pickup_stop_directive_overlay_text">Start scanning to continue</string>
    <string name="start_group_delivery_button_text">Continue</string>
    <string name="exit_property_button_text">Exit Property</string>
    <string name="exit_property_access_expired_text">Access has expired. Please contact driver support or call the customer.</string>
    <string name="verify_packages_button_text">Verify Packages</string>
    <string name="scan_verify_button_text">Scan to verify</string>
    <string name="group_delivery_packages_button_text">Deliver</string>
    <string name="tr_begin_execution_failed">Failed to begin TR Execution</string>
    <string name="scan_parcels_button_text">SCAN 1 PARCEL</string>
    <string name="return_package_list_activity_title">Return Packages</string>
    <string name="return_package_list_button_text">I\\\'ve Returned the Package(s)</string>
    <string name="return_package_list_text">Return package(s) to origin</string>
    <string name="return_items_summary_activity_title">Return</string>
    <string name="return_items_summary_button_text">I\\\'ve returned the item(s)</string>
    <string name="return_items_summary_continue_button_text">CONTINUE</string>
    <string name="return_empty_package_title">Return empty packaging</string>
    <string name="return_empty_package_text">Hand them to the station associate</string>
    <string name="amazon_shipping_dialog_title_text">Amazon Shipping is handling this delivery</string>
    <string name="amazon_shipping_dialog_first_paragraph_text">The customer may not have placed this order on Amazon. The seller is using Amazon Shipping for the delivery, but the customer may not know that.</string>
    <string name="amazon_shipping_dialog_second_paragraph_text">If you contact the customer, tell them you\\\'re from Amazon Shipping and are delivering an order from a different seller.</string>
    <string name="amazon_shipping_dialog_button_text">Got it</string>
    <string name="transport_request_detail_title_activity_completed_stop">COMPLETED STOP</string>
    <string name="transport_request_detail_deliver_title_activity">DELIVER</string>
    <string name="transport_request_detail_pickup_title_activity">PICK UP</string>
    <string name="transport_request_detail_arrive_title_activity">ARRIVE</string>
    <string name="transport_request_detail_title">Attended Delivery</string>
    <string name="transport_request_detail_packages">%d packages:</string>
    <string name="transport_request_detail_deliver_packages">Start delivery</string>
    <string name="transport_request_detail_arrive_at_stop">Arrive at stop</string>
    <string name="transport_request_detail_deliver_problem">Problem</string>
    <string name="transport_request_detail_navigate">Navigate</string>
    <string name="transport_request_detail_deliver_text_deliver">Deliver</string>
    <string name="transport_request_detail_deliver_text_deliver_before">Deliver by</string>
    <string name="transport_request_detail_deliver_text_delivered">Delivered</string>
    <string name="transport_request_detail_deliver_text_delivery_failed">Delivery Failed</string>
    <string name="transport_request_detail_deliver_text_delivery_rejected">Delivery Rejected</string>
    <string name="transport_request_detail_call_customer">Call customer</string>
    <string name="transport_request_detail_call_customer_calling">Calling…</string>
    <string name="transport_request_detail_call_customer_toast_succeed">Calling Customer…\n(You\\\'ll receive a phone call with the customer shortly)</string>
    <string name="transport_request_detail_call_customer_toast_fail">Could not call customer</string>
    <string name="transport_request_detail_call_customer_toast_fail_customer_number_unavailable">Customer number not available.</string>
    <string name="transport_request_detail_call_customer_toast_fail_cellular_connection">Make sure you have cellular coverage to call the customer.</string>
    <string name="transport_request_detail_call_customer_toast_fail_network">Connect to the internet to call the customer.</string>
    <string name="transport_request_detail_call_customer_toast_fail_server_error">We encountered a server error. Please try to call again.</string>
    <string name="transport_request_detail_call_customer_toast_fail_server_error_invalided_destination_number">Customer number is not valid.</string>
    <string name="transport_request_detail_call_customer_toast_fail_server_error_invalided_source_number">Your number is not valid. Please update and try to call again.</string>
    <string name="transport_request_detail_call_agent">Call Support Agent</string>
    <string name="transport_request_detail_call_agent_calling">Calling agent…</string>
    <string name="transport_request_detail_call_agent_notification_succeed">Calling Support Agent…\n(You\\\'ll receive a phone call with a Support agent shortly)</string>
    <string name="transport_request_detail_call_agent_notification_fail_network">Connect to the internet to call a support agent.</string>
    <string name="transport_request_detail_unknown_call_target">Unknown call target.</string>
    <string name="transport_request_detail_customer_instructions">Customer Instructions</string>
    <string name="transport_request_detail_delivery_notes">Instructions from customer</string>
    <string name="transport_request_detail_age_restriction_header">This is an Age-verified order</string>
    <string name="transport_request_detail_age_restriction_text">You\\\'ll be prompted to verify age</string>
    <string name="transport_request_detail_contact_manager">Call Support if you have additional delivery issues.</string>
    <string name="transport_request_detail_delivery_successful_at">Delivered at</string>
    <string name="transport_request_detail_delivery_unsuccessful_at">Delivery unsuccessful at</string>
    <string name="transport_request_detail_delivery_unsuccessful_reason_pre">Reason: </string>
    <string name="transport_request_detail_delivery_type_attended">Recipient Required</string>
    <string name="transport_request_detail_delivery_type_verify_age">Delivery – &lt;![CDATA[&lt;span&gt;Verify Age&lt;/span&gt;]]&gt;</string>
    <string name="transport_request_detail_delivery_type_unattended">Delivery</string>
    <string name="transport_request_detail_delivery_type_unattended_no_recipient_needed">No Recipient Required</string>
    <string name="transport_request_detail_delivery_type_unattended_do_not_disturb">No Recipient Required - don\\\'t knock or ring</string>
    <string name="transport_request_detail_delivery_type_open_box_delivery">Open Box Delivery</string>
    <string name="transport_request_detail_delivery_type_locker">Deliver to Locker</string>
    <string name="transport_request_detail_delivery_type_non_amazon_locker">Deliver to Non-Amazon locker</string>
    <string name="transport_request_detail_delivery_type_locker_pickup">Pickup at Locker</string>
    <string name="transport_request_detail_delivery_type_store">Deliver to Store</string>
    <string name="transport_request_detail_secure_delivery_type_in_home">In-Home Delivery</string>
    <string name="transport_request_detail_secure_delivery_type_in_garage">In-Garage Delivery</string>
    <string name="transport_request_detail_position_marker">%1$s</string>
    <string name="transport_request_detail_start_here_marker">Start</string>
    <string name="transport_request_detail_pickup_by">Arrive by</string>
    <string name="transport_request_detail_pickup_successful_at">Picked up at</string>
    <string name="transport_request_detail_pickup_unsuccessful_at">Pickup unsuccessful at %s</string>
    <string name="transport_request_detail_packages_for_customer">%1$d package%2$s for %3$s</string>
    <string name="transport_request_detail_arrived_at">Arrived at</string>
    <string name="transport_request_detail_arrive_by">Arrive by</string>
    <string name="transport_request_detail_options_title">What\\\'s the problem\?</string>
    <string name="transport_request_detail_options_customer_return_header">Return items</string>
    <string name="transport_request_detail_options_customer_return_text">Remove items that are damaged or the customer doesn\\\'t want.</string>
    <string name="transport_request_detail_options_customer_unable_to_deliver_header">Unable to deliver</string>
    <string name="transport_request_detail_options_customer_unable_to_deliver_text">Skip this delivery and bring all items back to the station.</string>
    <string name="transportation_mode_title_activity">MODE OF TRANSPORTATION</string>
    <string name="transportation_mode_failure">Setting transportation mode failed.</string>
    <string name="transportation_mode_failure_network">Setting transportation mode failed. Check network connectivity.</string>
    <string name="transportation_mode_header">How are you delivering packages\?</string>
    <string name="transportation_mode_ok_button">\@android:string/ok</string>
    <string name="availability_failure">Setting availability failed.</string>
    <string name="availability_failure_network">Setting availability failed. Check network connectivity.</string>
    <string name="transport_request_group_title_activity">STOPS</string>
    <string name="transport_request_group_status_default">Delivery</string>
    <string name="transport_request_group_status_pickup">Pickup</string>
    <string name="transport_request_group_status_attended_delivery">Attended Delivery</string>
    <string name="transport_request_group_status_unattended_delivery">Unattended Delivery</string>
    <string name="transport_request_group_status_success_delivery">Delivered</string>
    <string name="transport_request_group_status_success_pickup">Picked up</string>
    <string name="transport_request_group_status_success_exceptions">Delivered with exceptions</string>
    <string name="transport_request_group_subheader_undelivered">Undeliverable</string>
    <string name="transport_request_group_subheader_completed">Completed</string>
    <string name="transport_request_group_pickup_title_activity">PICKUP</string>
    <string name="transport_request_group_completed_title">Wait for new stops</string>
    <string name="transport_request_group_completed_off_duty">You are not on duty</string>
    <string name="transport_request_group_completed_message_off_duty">Go on duty to start making deliveries.</string>
    <string name="transport_request_group_completed_button_off_duty">Go on duty</string>
    <string name="transport_request_group_completed_message">Assignments will automatically show up on your stops list</string>
    <string name="transport_request_group_completed_button">Scan packages for pickup</string>
    <string name="transport_request_group_completed_off_duty_button">Go off duty</string>
    <string name="transport_request_group_sync_failed_network">Sync Failed: No network</string>
    <string name="transport_request_group_sync_failed">Sync failed</string>
    <string name="transport_request_group_sync_success">Sync was successful</string>
    <string name="transport_request_group_in_progress">In progress</string>
    <string name="new_stops_dialog_title_stops">New Stops</string>
    <string name="new_stops_dialog_title_station">New Stop</string>
    <string name="new_stops_dialog_title_waypoint">Waiting Area</string>
    <string name="new_stops_dialog_button_continue">Continue</string>
    <string name="new_stops_dialog_continue_instructions">Continue your current stop. Your new stop(s) have been added later in your itinerary.</string>
    <string name="new_stops_dialog_station_instructions">Head to the delivery station for more delivery opportunities</string>
    <string name="new_stops_dialog_waypoint_instructions">Head to waiting area for more delivery opportunities</string>
    <string name="new_stops_dialog_continue_button">Acknowledge</string>
    <string name="new_stops_dialog_start_button">Acknowledge and begin</string>
    <string name="signature_title_activity">RECIPIENT SIGNATURE</string>
    <string name="signature_background_instruction">Sign Here</string>
    <string name="signature_not">Not %1$s\?</string>
    <string name="signature_done_button">done</string>
    <string name="scan_title_activity">SCAN PACKAGES</string>
    <string name="scan_title_pickup_config_activity">SCAN</string>
    <string name="scan_title_activity_undo">REMOVE ORDERS</string>
    <string name="scan_title_activity_leave">LEAVE PACKAGE</string>
    <string name="scan_title_activity_delivery">DELIVER</string>
    <string name="scan_more_packages_message_start">Start scanning to continue</string>
    <string name="scan_more_packages_message_remaining">Scan remaining packages</string>
    <string name="scan_finish_button_message_text">To add more packages, continue scanning.</string>
    <string name="scan_continue_delivery">Continue delivery</string>
    <string name="scan_finish_button_deliver">Finish delivery</string>
    <string name="scan_row_number">%1$d of %2$d</string>
    <string name="scan_barcode_scanned">Looking up the scanned package…</string>
    <string name="scan_feedback_barcode_already_scanned">You\\\'ve already scanned this package.</string>
    <string name="scan_feedback_wrong_barcode">Wrong barcode scanned.</string>
    <string name="scan_loading_title">Scanned package:</string>
    <string name="scan_order_no_longer_assigned">These packages are no longer assigned to you.</string>
    <string name="scan_leave_these_packages_behind">Leave these packages behind:</string>
    <string name="scan_pickup_dont_pickup_bags">Don\\\'t pick up these packages:</string>
    <string name="scan_pickup_leave_bags_here">Leave them here.</string>
    <string name="scan_start_instructions">The attendant on duty will hand packages to you. Scan all packages with your device.</string>
    <string name="scan_start_instructions_CN51">Use the scanner on your device to scan the barcode on each package.</string>
    <string name="scan_instructions_manual_scan">Enter the package ID</string>
    <string name="scan_row_loading_message">Loading…</string>
    <string name="scan_row_error_title">Scanned package:</string>
    <string name="scan_row_error_message_network">Error: Check network connectivity</string>
    <string name="scan_row_footer_message_instruction">Scan all packages to complete order</string>
    <string name="scan_row_footer_message_total">Total packages</string>
    <string name="scan_row_footer_numbers_loading">%1$d of…</string>
    <string name="scan_remove_orders">Remove %1$d order%2$s</string>
    <string name="scan_finishing_pickup">Finishing pickup…</string>
    <string name="scan_finishing_delivery">Finishing delivery…</string>
    <string name="scan_bag_number">Bag number</string>
    <string name="do_not_deliver_package">Don\\\'t deliver this package</string>
    <string name="do_not_pickup_package">Leave this package</string>
    <string name="scan_undo_force_toast">You must remove all orders to continue</string>
    <string name="scan_wrong_barcode_inline_error_message">Wrong barcode</string>
    <string name="scan_no_internet_inline_error_message">No internet</string>
    <string name="default_staging_location">At Store</string>
    <string name="scan_item_container_id_label">Container ID</string>
    <string name="scan_item_location_label">Location</string>
    <string name="scan_item_package_id_label">Package ID</string>
    <string name="scan_item_barcode_label">Barcode</string>
    <string name="scan_item_package_count_label">Packages</string>
    <string name="scan_item_package_count">%1$d</string>
    <string name="scan_item_container_scan_count">%1$d of %2$d</string>
    <string name="scan_item_not_in_container">Not in a Container</string>
    <string name="scan_loading_data_progress">Retrieving scan information…</string>
    <string name="scan_loading_data_failed">Failed to load data. Please rescan packages.</string>
    <string name="scan_recovery_data_title">Package scan recovery</string>
    <string name="scan_recovery_continue_button">Continue scanning</string>
    <string name="scan_recovery_failure_header">Unable to retrieve packages</string>
    <string name="scan_recovery_failure_message">Please rescan the packages.</string>
    <string name="scan_error_title">ERROR</string>
    <string name="scanning_complete">Scanning Complete</string>
    <string name="scan_section_header_high_volume_stop">Going to stop 1</string>
    <string name="scan_section_header_other_stops">Other stops</string>
    <string name="scan_section_header_overflow_packages">Overflow packages</string>
    <string name="configure_scan_button_text">Start Scanning</string>
    <string name="skip_scan_button_text">Swipe to finish pickup</string>
    <string name="skip_scan_finish_button_text">Finish pickup</string>
    <string name="driver_scanning_instructions_text">Scan all packages. If you are unable to pick up a listed package, report the problem in the Help menu to complete scanning.</string>
    <string name="wayfinding_scan_incomplete_substop_error">You cannot continue without scanning all packages for one address.</string>
    <string name="wayfinding_selection_continue_button_in_box">I am at the secure box</string>
    <string name="scanless_stop_review_title_activity">REVIEW</string>
    <string name="scanless_stop_item_recipient">For %1$s</string>
    <string name="scanless_stop_options_unable_to_pickup_header">Unable to pick up</string>
    <string name="scanless_stop_options_unable_to_pickup_text">Mark all orders at this stop as unavailable. You\\\'ll continue with other orders or return to the station.</string>
    <string name="scanless_stop_options_partial_pickup_header">Partial pickup</string>
    <string name="scanless_stop_options_partial_pickup_text">Mark the orders that are unavailable. You\\\'ll continue with other orders or return to the station.</string>
    <string name="pickup_options_title">Options</string>
    <string name="pickup_options_remove_pickup_header">Remove packages</string>
    <string name="options_unscan_package">Unscan packages</string>
    <string name="pickup_options_remove_pickup_text">Leave packages at the station. If you have picked up multiple orders, you can remove them individually.</string>
    <string name="options_enter_package_id_header">Manually enter package ID</string>
    <string name="options_enter_barcode_id_header">Manually enter container ID or package ID</string>
    <string name="options_report_single_package_missing">Report the package as missing</string>
    <string name="options_report_packages_missing">Report the rest of the packages as missing</string>
    <string name="options_skip_scanning_packages">Skip scanning packages</string>
    <string name="options_skip_travel_to_stop">Skip travel to stop</string>
    <string name="options_skip_geofence">Skip geofence</string>
    <string name="dsp_pickup_options_show_address_details">Show address details</string>
    <string name="dsp_pickup_options_hide_address_details">Hide address details</string>
    <string name="transport_request_detail_address_landmark">Landmark: %1$s</string>
    <string name="return_reason_main_text_package_damaged">Package is damaged</string>
    <string name="return_reason_main_text_items_missing">Item is missing</string>
    <string name="return_reason_main_text_package_missing">Package is missing</string>
    <string name="return_reason_main_text_incorrect">Item is incorrect</string>
    <string name="return_reason_main_text_package_incorrect">Package is incorrect</string>
    <string name="return_reason_main_text_order_unwanted">%1$s doesn\\\'t want the order anymore</string>
    <string name="return_reason_main_text_package_unwanted">%1$s doesn\\\'t want the packages anymore</string>
    <string name="return_reason_main_text_items_unwanted">%1$s no longer wants a particular item</string>
    <string name="return_reason_main_text_age_verification">%1$s did not meet minimum required age</string>
    <string name="return_reason_main_text_product_quality">%1$s rejected due to product quality</string>
    <string name="return_reason_main_text_carrier_delay">%1$s rejected due to carrier delay</string>
    <string name="return_reason_main_text_missing_parts">%1$s rejected due to missing parts</string>
    <string name="return_reason_main_text_unexpected_item">%1$s rejected due to unexpected item</string>
    <string name="return_reason_main_text_product_damage">%1$s rejected due to product damage</string>
    <string name="return_reason_main_text_bag_id_mismatch">Rejected - Bag Id Mismatch</string>
    <string name="return_reason_edit_mode_activity_title">RETURN</string>
    <string name="return_reason_view_only_mode_activity_title">RETURNED</string>
    <string name="return_reason_result_not_required">Rejected - Not Required</string>
    <string name="return_reason_result_order_mismatch">Rejected - Order Mismatch</string>
    <string name="return_reason_result_Damaged">Rejected - Damaged</string>
    <string name="return_reason_title">What\\\'s the problem\?</string>
    <string name="return_reason_damaged_title">Which package(s) are damaged\?</string>
    <string name="return_reason_items_missing_title">Which items are missing\?</string>
    <string name="return_reason_packages_missing_title">Which packages are missing\?</string>
    <string name="return_reason_incorrect_title">Which items are incorrect\?</string>
    <string name="return_reason_package_incorrect_title">Which packages are incorrect\?</string>
    <string name="return_reason_unwanted_title">Which item(s) does the customer no longer want\?</string>
    <string name="return_reason_unwanted_package_title">Which packages does the customer no longer want\?</string>
    <string name="return_reason_product_quality_title">Which packages have product quality problem\?</string>
    <string name="return_reason_carrier_delay_title">Which packages are too late to deliver\?</string>
    <string name="return_reason_missing_parts_title">Which packages are missing parts\?</string>
    <string name="return_reason_unexpected_item_title">Which item(s) is unexpected\?</string>
    <string name="return_reason_product_damage_title">Which packages are damaged\?</string>
    <string name="return_reason_bag_id_mismatch_title">Which Bag Id(s) are not matching\?</string>
    <string name="return_reason_result_title">Unable to deliver because</string>
    <string name="return_reason_result_title_divert">Stop canceled because</string>
    <string name="return_reason_result_unwanted">Customer doesn\\\'t want their order anymore.</string>
    <string name="return_reason_result_unwanted_package">Customer doesn\\\'t want their packages anymore.</string>
    <string name="return_reason_result_damaged">Package is damaged</string>
    <string name="return_reason_result_age_verification">Age verification failed</string>
    <string name="return_reason_result_incorrect">Package is incorrect</string>
    <string name="return_reason_result_missing">Package is missing</string>
    <string name="return_reason_result_product_quality">Customer rejected due to product quality</string>
    <string name="return_reason_result_carrier_delay">Customer rejected due to carrier delay</string>
    <string name="return_reason_result_missing_parts">Customer rejected due to missing parts</string>
    <string name="return_reason_result_unexpected_item">Rejected due to unexpected item</string>
    <string name="return_reason_result_product_damage">Customer rejected due to product damage</string>
    <string name="return_reason_result_bag_id_mismatch">Rejected - Bag Id Mismatch</string>
    <string name="return_reason_result_item_incorrect">Item is incorrect</string>
    <string name="exchange_reason_result_title">Unable to exchange because</string>
    <string name="return_items_wait_title">WAIT</string>
    <string name="return_items_splash_title">Wait until you arrive</string>
    <string name="return_items_splash_message">If there are problems with individual items, you can indicate it when you deliver this customer\\\'s order.</string>
    <string name="return_items_mark_undeliverable_warning">The order will be marked as undeliverable.</string>
    <string name="return_items_not_on_list">Item isn\\\'t on the list</string>
    <string name="return_items_continue_delivering">Continue Delivering</string>
    <string name="return_items_continue">Continue</string>
    <string name="return_items_reason_code_none">None</string>
    <string name="return_items_reason_code_item_damaged">Damaged</string>
    <string name="return_items_reason_code_item_incorrect">Incorrect</string>
    <string name="return_items_reason_code_no_longer_wanted">No longer wanted</string>
    <string name="return_items_reason_code_rejected">Rejected</string>
    <string name="return_items_reason_code_item_missing">Missing</string>
    <string name="return_items_reason_code_late_deliery">Late for delivery</string>
    <string name="services_canceled_product_defective">Product is defective</string>
    <string name="services_canceled_service_window_timeout">No time to perform service within slot</string>
    <string name="services_canceled_location_not_ready">In-Home location not ready</string>
    <string name="services_canceled_missing_tools">Missing tools or parts</string>
    <string name="services_canceled_not_trained">Not trained to perform service</string>
    <string name="services_canceled_customer_rejected_service">Customer does not need the service</string>
    <string name="services_canceled_not_able_to_haul_away">Not able to perform haul-away service</string>
    <string name="weigh_station_checkin_alert_title">Break for weigh stations!</string>
    <string name="weigh_station_checkin_alert_description">Don\\\'t forget, you are operating a Department of Transportation (DOT) regulated vehicle. If you see an open weigh station, you are required to follow signage and enter the weigh station for inspection.</string>
    <string name="weigh_station_checkin_alert_button">Got it</string>
    <string name="remove_items_title_activity">RETURN</string>
    <string name="remove_items_return_title">Return items</string>
    <string name="remove_items_select_items">Select %1$d item%2$s</string>
    <string name="remove_items_deselect_items">Deselect %1$d item%2$s</string>
    <string name="remove_items_return_button">Return %1$d item%2$s</string>
    <string name="remove_items_return_button_extras">You\\\'ll confirm reasons in the next step.</string>
    <string name="remove_items_done_button">Done</string>
    <string name="remove_items_reason_title_activity">REASON FOR RETURN</string>
    <string name="remove_items_reason_ok_button">Confirm</string>
    <string name="remove_items_reason_reason">Reason</string>
    <string name="remove_items_reason_select_reason">Select reason</string>
    <string name="remove_items_reason_options_title">Mark all as</string>
    <string name="sync_title_activity">SYNC</string>
    <string name="sync_title">Sync</string>
    <string name="sync_instructions_title">Sync delivery data</string>
    <string name="sync_instructions">We\\\'re having difficulty syncing information from your device. Tap below to try again.</string>
    <string name="sync_talk_to_manager">Call Support if you\\\'re having trouble.</string>
    <string name="sync_retry_sync_button">Retry sync</string>
    <string name="sync_retry_sync_progress">Trying to connect</string>
    <string name="notification_system_title">Amazon Flex</string>
    <string name="notification_rabbit_headline">New delivery notification</string>
    <string name="notification_details">Tap to view and acknowledge</string>
    <string name="notification_button">Acknowledge and start</string>
    <string name="notification_age_verified">Age verified</string>
    <string name="notification_delivery_complete">Delivery completed</string>
    <string name="notification_exchange_complete">Exchange completed</string>
    <string name="notification_delivery_complete_partial">Delivery completed with exceptions</string>
    <string name="notification_delivery_late">This delivery is late. Call the customer to see if they still want this delivery.</string>
    <string name="notification_unable_to_deliver">Completed without delivering</string>
    <string name="notification_unable_to_exchange">Delivery completed but exchange canceled.</string>
    <string name="notification_invalid_scan">Package scanned is not part of order</string>
    <string name="notification_tech_error">We encountered a technical error. Please try again later.</string>
    <string name="notification_tech_error_go_back_and_retry">We encountered a technical error. Please go back and try again.</string>
    <string name="notification_order_not_added">One or more orders could not be added at this time.</string>
    <string name="notification_schedule_update">Your Calendar has been updated.</string>
    <string name="notification_schedule_update_title">Schedule Update</string>
    <string name="notification_work_alert">You\\\'ve got a pickup in 120 minutes</string>
    <string name="notification_work_alert_title">Schedule Alert</string>
    <string name="notification_flex_work_alert">New blocks are available but may go quickly.</string>
    <string name="notification_flex_work_alert_title">Deliver today!</string>
    <string name="notification_onboard_complete">Open the Amazon Flex app to get started.</string>
    <string name="notification_onboard_complete_title">You\\\'re in business!</string>
    <string name="notification_connect_to_internet">Connect to the internet</string>
    <string name="notification_sync_instructions">You need internet connectivity to sync information on your device</string>
    <string name="notification_package_ready_text">Head inside. Your packages are ready to pick up.</string>
    <string name="notification_offline_maps_download_scheduled">Offline Maps download scheduled</string>
    <string name="notification_offine_maps_downoad_delete">Offline maps download delete scheduled</string>
    <string name="notification_offline_maps_no_downloads">There are no maps data downloaded.</string>
    <string name="notification_offline_maps_update_prompt_download">As part of the recent upgrade, we need to update your offline maps. Tap to begin update.</string>
    <string name="notification_offline_maps_update_remind_me_later">We\\\'ll remind you the next time you open the app.</string>
    <string name="notification_offline_maps_update_download_in_progress">Your offline maps are downloading</string>
    <string name="notification_offline_maps_update_download_failed">There was an error downloading your offline maps. Use the offline maps screen to resume or cancel your downloads.</string>
    <string name="notification_offline_maps_update_download_dismissed">We won\\\'t remind you again, but you can download offline maps later if needed.</string>
    <string name="notification_delivery_will_be_recorded">This delivery may be recorded</string>
    <string name="notification_force_lock_door">Lock door to complete</string>
    <string name="notification_check_door_locked">Check door is locked</string>
    <string name="notification_switch_to_standard_delivery_home">Unable to download lock information. Switching to doorstep delivery.</string>
    <string name="notification_comingled_stop_complete">Delivery and pickup completed</string>
    <string name="notification_comingled_stop_complete_partial">Delivery and pickup completed with exceptions</string>
    <string name="notification_commingled_stop_failed">Completed without delivering or picking up</string>
    <string name="notification_services_canceled">Service(s) cancelled</string>
    <string name="pickup_completed_text">Pickup completed</string>
    <string name="pickup_partially_completed_text">Pickup completed with exceptions</string>
    <string name="pickup_failed_text">Completed without picking up</string>
    <string name="notification_gps_disabled">Please enable GPS in settings</string>
    <string name="notification_no_email_providers">No email providers found.</string>
    <string name="notification_no_phone_number">No transporter phone number found.</string>
    <string name="notification_account_already_exists">You have signed in with your existing account.</string>
    <string name="notification_error_code">Support code: %1$d</string>
    <string name="notification_instant_offer_available">You\\\'re signed in and available now</string>
    <string name="notification_instant_offer_unavailable">You\\\'re signed in</string>
    <string name="notification_instant_offer_available_detail">Waiting for nearby offers.</string>
    <string name="notification_instant_offer_unavailable_detail">Available for upcoming or immediate deliveries\?</string>
    <string name="notification_action_instant_offer_view">OFFERS</string>
    <string name="notification_action_instant_offer_become_available">AVAILABLE NOW</string>
    <string name="notification_action_instant_offer_become_unavailable">UNAVAILABLE NOW</string>
    <string name="notification_deliver_remaining_packages">Deliver remaining packages</string>
    <string name="notification_server_error_login_again">A server error has occurred. Please try to log out and log back in.</string>
    <string name="offer_extension_notification_title">New offer for higher pay</string>
    <string name="offer_extension_notification_message">Accept within 2 minutes. A new, longer block is available for additional pay. Tap to view details.</string>
    <string name="location_disabled_title">Turn on Location Services</string>
    <string name="location_disabled_message">GPS is required for navigation. Turn on location services to continue.</string>
    <string name="location_disabled_button_text">Open Settings</string>
    <string name="airplaneMode_enabled_title">Airplane Mode is Enabled</string>
    <string name="airplaneMode_enabled_message">Turn off airplane mode to continue.</string>
    <string name="airplaneMode_enabled_button_text">Open Settings</string>
    <string name="open_settings">Open Settings</string>
    <string name="location_not_found_title">GPS not available</string>
    <string name="location_not_found_text">The GPS signal is too weak or not available.\n\nTo try again later, open the app.</string>
    <string name="location_not_found_button">CLOSE APP</string>
    <string name="notification_error">We encountered a technical error</string>
    <string name="network_tech_error">There was a network issue</string>
    <string name="valid_id_delivery_reason_title">Age verified</string>
    <string name="valid_id_delivery_reason_text">Recipient is at the delivery address and is age %1$d or over.</string>
    <string name="valid_id_delivery_reason_text_id_check">Recipient is at the delivery address, is age %1$d or over, and has valid photo ID</string>
    <string name="invalid_id_partial_delivery_reason_title">Delivery exceptions</string>
    <string name="invalid_id_partial_delivery_reason_text">%d package(s) couldn\\\'t be delivered because recipient did not meet age/identity requirements.</string>
    <string name="invalid_id_alcohol_header">Don\\\'t deliver age restricted package(s).</string>
    <string name="invalid_id_partial_return">Keep the following package(s) with you.</string>
    <string name="invalid_id_complete_return">Keep all the packages with you and continue to your other deliveries.</string>
    <string name="invalid_id_page_title">Remove</string>
    <string name="contact_us_title_activity">Contact Us</string>
    <string name="help_title">Help</string>
    <string name="help_need_body">Call the customer in the following situations:\n•You\\\'re going to be late\n•You can\\\'t find the address\n•You can\\\'t access the building\n\nContact Support for all other problems.</string>
    <string name="help_eula_button">Read License and Agreement</string>
    <string name="help_version_title">Version info</string>
    <string name="help_version_message">Package Name: %1$s\nVersion Name: %2$s\nVersion Code: %3$d\nLast Updated: %4$s\nSerial: %5$s</string>
    <string name="help_app_version">Version :: %1$s</string>
    <string name="help_screen_send_email_text">Send email</string>
    <string name="help_screen_call_dispatcher_text">Call dispatcher</string>
    <string name="help_screen_header_text">Need help\?</string>
    <string name="help_screen_on_duty_content_text">For urgent delivery issues, tap &lt;b&gt;Call dispatcher&lt;/b&gt;. For other issues, tap &lt;b&gt;Send email&lt;/b&gt; and use the email account that you used to sign up for Amazon Flex. Don\\\'t include personal or confidential information.</string>
    <string name="help_screen_on_duty_no_call_dispatcher_content_text">Use the email account that you used to sign up for Amazon Flex. Don\\\'t include personal or confidential information.</string>
    <string name="help_screen_off_duty_content_text">For payment issues, tap &lt;b&gt;Menu&lt;/b&gt; &amp;gt; &lt;b&gt;Earnings&lt;/b&gt;. Tap the problematic payment, and then tap &lt;b&gt;Report a problem&lt;/b&gt;.\n\nFor other issues, send us an email using the email account that you used to sign up for Amazon Flex. Don\\\'t include personal or confidential information.</string>
    <string name="help_screen_off_duty_call_dispatcher_content_text">For payment issues, tap &lt;b&gt;Menu&lt;/b&gt; &amp;gt; &lt;b&gt;Earnings&lt;/b&gt;. Tap the problematic payment, and then tap &lt;b&gt;Report a problem&lt;/b&gt;.\n\nFor other issues, tap &lt;b&gt;Call Dispatcher&lt;/b&gt;.</string>
    <string name="help_needed_email_subject">Amazon Flex Help for %1$s</string>
    <string name="healthcare_subsidy_email_subject">Healthcare Subsidy Questions: Flex CA</string>
    <string name="healthcare_subsidy_email_content_0">Hello,</string>
    <string name="healthcare_subsidy_email_content_1">Thank you for contacting Amazon Flex Support.</string>
    <string name="healthcare_subsidy_email_content_2">Describe your question or concern here:</string>
    <string name="healthcare_subsidy_email_content_3">You may expect a reply via email within the next few days.</string>
    <string name="healthcare_subsidy_email_content_4">Thank you,</string>
    <string name="healthcare_subsidy_email_content_5">Amazon Flex Support</string>
    <string name="help_needed_email_choose_client_instruction">Choose an app to send email</string>
    <string name="help_option_flex_program">Amazon Flex Program</string>
    <string name="help_option_flex_app">Amazon Flex App</string>
    <string name="help_option_scheduling">Scheduling and Your Calendar</string>
    <string name="help_option_requirements">Requirements</string>
    <string name="help_option_insurance">Insurance</string>
    <string name="help_option_getting_paid">Getting Paid</string>
    <string name="help_option_taxes">Taxes</string>
    <string name="help_option_contact_us">Contact us</string>
    <string name="check_id_title_activity">VERIFY</string>
    <string name="verify_age_activity_title">VERIFY AGE</string>
    <string name="avd_fragment_challenge_subheader">Does the recipient look over %1$d\?</string>
    <string name="avd_fragment_challenge_instruction">If the recipient looks younger than %1$d, check their ID to make sure they\\\'re at least %2$d.</string>
    <string name="avd_fragment_age_subinstruction">The recipient is over %1$d if born on or before</string>
    <string name="avd_fragment_challenge_valid_button">Yes, they look over %1$d</string>
    <string name="avd_fragment_challenge_invalid_button">No, they don\\\'t look over %1$d</string>
    <string name="avd_fragment_age_invalid_button">Not old enough / no ID</string>
    <string name="avd_fragment_age_subheader">Confirm the recipient\\\'s age</string>
    <string name="avd_fragment_age_instruction">Check the recipient\\\'s ID card or Passport and confirm their name, photo, and age.</string>
    <string name="avd_fragment_age_valid_button">Enter date of birth</string>
    <string name="avd_fragment_identity_subheader">Confirm the recipient\\\'s identity</string>
    <string name="avd_fragment_identity_instruction">Check the recipient\\\'s ID card or Passport and confirm their name and photo.</string>
    <string name="avd_fragment_identity_subinstruction">Recipient name</string>
    <string name="avd_fragment_identity_valid_button">Continue</string>
    <string name="avd_fragment_identity_invalid_button">Doesn\\\'t match / no ID</string>
    <string name="avd_fragment_scan_instruction">Ask for the recipient\\\'s driver\\\'s license to verify their age on the next screen.</string>
    <string name="avd_fragment_scan_valid_button">Scan Driver\\\'s License</string>
    <string name="scan_dl_fragment_manual_button">Enter Manually</string>
    <string name="check_id_title">Verify age</string>
    <string name="check_id_subheader_text">Is the recipient %1$d or older\?</string>
    <string name="check_id_instructions_text">Check their ID to make sure they\\\'re %1$d or older.</string>
    <string name="check_id_valid_age_button_text">Yes, they\\\'re %1$d or older</string>
    <string name="check_id_invalid_age_button_text">Under %1$d / no id</string>
    <string name="check_id_display_text">Ask to see the recipient\\\'s ID.</string>
    <string name="check_id_display_enter_date">Enter the date of birth on the ID</string>
    <string name="check_id_no_valid_id_text">No Valid ID</string>
    <string name="check_id_ok_id_text">Valid ID</string>
    <string name="check_id_illegal_birthdate_entered">Year of birth doesn\\\'t meet Amazon Flex requirements.</string>
    <string name="check_id_customer_invalid_age">Customer too young\nMinimum age is: %1$d</string>
    <string name="training_flavor_dialog_title">For training use only</string>
    <string name="training_flavor_dialog_message">This version of the app is for training use only.</string>
    <string name="options_menu_sync">Sync</string>
    <string name="options_menu_options">Options</string>
    <string name="options_menu_call">Call</string>
    <string name="options_menu_select_all">Select all</string>
    <string name="options_menu_select_entire_order">Select entire order</string>
    <string name="options_menu_deselect_entire_order">Deselect all</string>
    <string name="options_menu_light">Turn Flash On</string>
    <string name="welcome_message">Debug</string>
    <string name="gps_dialog_title">GPS is disabled</string>
    <string name="gps_dialog_text">Please open the GPS settings.</string>
    <string name="gps_dialog_button">\@android:string/ok</string>
    <string name="phone_number_title_activity">ENTER DEVICE PHONE NUMBER</string>
    <string name="phone_number_title">Enter the phone number for this device.</string>
    <string name="phone_number_display_text">Phone number</string>
    <string name="country_code_display_text">Country code</string>
    <string name="phone_number_continue_button_text">\@android:string/ok</string>
    <string name="phone_number_action_label">\@string/phone_number_continue_button_text</string>
    <string name="phone_number_keyboard_action">\@string/phone_number_continue_button_text</string>
    <string name="phone_number_shared_pref_key">DevicePhoneNumber</string>
    <string name="phone_number_hint">************</string>
    <string name="country_code_hint">1</string>
    <string name="phone_number_upload_failed">Could not upload phone number. Please try again.</string>
    <string name="phone_number_upload_failed_network">Network connection not found. Please connect to a network and try again.</string>
    <string name="phone_number_error">Invalid phone number</string>
    <string name="country_code_plus_sign">+</string>
    <string name="country_code_action_label">Next</string>
    <string name="country_code_error">Invalid country code</string>
    <string name="service_area_title">Delivery Area</string>
    <string name="signature_spinner_title_activity">DELIVER</string>
    <string name="signature_spinner_title_attended">Package delivered to…</string>
    <string name="signature_spinner_display_text_attended">Choose a recipient</string>
    <string name="signature_spinner_title_unattended">Where are you leaving the packages\?</string>
    <string name="signature_spinner_display_text_unattended">Choose a location</string>
    <string name="signature_spinner_recipient_name_text">Enter the recipient\\\'s name</string>
    <string name="signature_spinner_recipient_customer">%1$s, Customer</string>
    <string name="signature_spinner_household_member">Household member</string>
    <string name="signature_spinner_customer_doorstep">Front door</string>
    <string name="signature_spinner_recipient_receptionist">Receptionist</string>
    <string name="signature_spinner_recipient_mailroom_clerk">Mailroom</string>
    <string name="signature_spinner_recipient_other">Other, by customer request</string>
    <string name="signature_spinner_recipient_toast">Delivery Complete</string>
    <string name="signature_spinner_call_button">Call customer</string>
    <string name="signature_spinner_instructions_default">Don\\\'t leave packages unattended, even if the customer asks you to do so.</string>
    <string name="exchange_activity_title">EXCHANGE</string>
    <string name="verify_exchange_activity_title">VERIFY</string>
    <string name="exchange_verify_button_text">Verify return items</string>
    <string name="exchange_item_doesnot_match">Item doesn\\\'t match</string>
    <string name="finish_exchange_message">Finishing exchange</string>
    <string name="convert_exchange_to_regular">CONVERT TO REGULAR DELIVERY</string>
    <string name="buyback_regular_spotlight_description">You can convert exchanges to a regular delivery</string>
    <string name="delivery_activity_title">DELIVER</string>
    <string name="delivery_activity_continue_button_text">Continue</string>
    <string name="finish_delivery_message">Finishing delivery</string>
    <string name="multiple_customers">Multiple orders</string>
    <string name="delivery_geofence_help_option_bypass_geofence">Bypass geofence</string>
    <string name="delivery_geofence_instruction_text">If the delivery address isn\\\'t in the highlighted area, call Support.</string>
    <string name="delivery_softblock_geofence_continue_delivering">Continue delivering</string>
    <string name="delivery_softblock_geofence_instructions_header">Outside of delivery area</string>
    <string name="delivery_softblock_geofence_instructions_body">You\\\'ll need to move inside the highlighted area to complete delivery.</string>
    <string name="delivery_softblock_geofence_instructions_action">Delivery pin not accurate\? Update it now</string>
    <string name="delivery_softblock_geofence_dialog_instruction">Drag the map to move the pin to the correct delivery location.</string>
    <string name="delivery_geocode_correction_instructions">Drag the map to move the pin to the correct delivery location.</string>
    <string name="delivery_softblock_geofence_dialog_continue">Continue</string>
    <string name="delivery_softblock_geofence_dialog_cancel">Cancel</string>
    <string name="delivery_geocode_correction_confirm_delivery_location">Confirm delivery location</string>
    <string name="delivery_softblock_geofence_dialog_confirm_message">Thank you for updating the delivery pin location!\n\nBy updating this delivery location, you are agreeing that this is the accurate location of the delivery address.</string>
    <string name="delivery_softblock_geofence_dialog_confirm_title">Delivery Location Correction</string>
    <string name="delivery_softblock_geofence_dialog_confirm_continue">Continue</string>
    <string name="delivery_softblock_geofence_dialog_confirm_cancel">Cancel</string>
    <string name="delivery_softblock_geofence_dialog_confirm_agree">I agree</string>
    <string name="geofence_continue_delivering">Continue delivering</string>
    <string name="geofence_continue">Continue</string>
    <string name="pickup_hardblock_geofence_with_reason_code_instructions_body">You must be at the pickup location to choose</string>
    <string name="pickup_hardblock_geofence_instructions_body">Move inside the orange circle to pickup.</string>
    <string name="pickup_hardblock_geofence_instructions_header">Outside of pickup area</string>
    <string name="call_support_geofence_instructions_action">Location issues\? &lt;![CDATA[&lt;span&gt;Call Support&lt;/span&gt;]]&gt;</string>
    <string name="delivery_hardblock_geofence_instructions_body">Move inside the orange area to deliver.</string>
    <string name="delivery_hardblock_geofence_with_reason_code_instructions_body">You must be at the delivery location to choose </string>
    <string name="pickup_activity_title">PICK UP</string>
    <string name="unpickupable_activity_title">UNABLE TO PICKUP</string>
    <string name="fsm_pickup_activity_title">SHOP</string>
    <string name="avd_activity_title">Verify Age</string>
    <string name="avd_id_selection_page_title">Request ID</string>
    <string name="avd_id_selection_header">What type of ID does the recipient have\?</string>
    <string name="avd_id_scan_page_title">Scan ID</string>
    <string name="avd_id_scan_page_header">Align barcode on back of ID with the guide below</string>
    <string name="avd_recepient_name_format">%1$s %2$s</string>
    <string name="avd_scan_success_feedback">Recipient Old Enough</string>
    <string name="avd_scan_negative_feedback">Recipient Not Old Enough</string>
    <string name="avd_scan_partial_feedback">More Info Required</string>
    <string name="avd_recipient_first_name_last_name">%1$s %2$s</string>
    <string name="avd_enter_manual_button">Not working\? Enter ID Info</string>
    <string name="avd_manual_entry_page_title">Enter ID Info</string>
    <string name="avd_manual_entry_header_all">Enter the recipient\\\'s birth date, full name and ID number.</string>
    <string name="avd_manual_entry_header_dob_name">Enter the recipient\\\'s birth date and full name.</string>
    <string name="avd_manual_entry_header_dob">Enter the recipient\\\'s birth date.</string>
    <string name="avd_manual_entry_header_generic">Enter the recipient\\\'s following information.</string>
    <string name="avd_manual_entry_continue_button_string">Get Signature</string>
    <string name="avd_full_name_hint">Full Name</string>
    <string name="avd_dob_hint">Birth Date</string>
    <string name="avd_id_number_hint">ID Number</string>
    <string name="avd_dob_success_msg">Recipient is old enough</string>
    <string name="avd_name_error_msg">Full name must be at least 2 characters</string>
    <string name="avd_id_number_error_msg">ID number must be 4-20 characters</string>
    <string name="avd_challenge_subheader">Does the recipient look over %1$d\?</string>
    <string name="avd_challenge_success_button_text">Yes, they look over %1$d</string>
    <string name="avd_challenge_failure_button_text">No, they don\\\'t look over %1$d</string>
    <string name="avd_electronic_signing_message">By swiping below I am electronically signing that I complied with the alcohol delivery process.</string>
    <string name="avd_id_scan_electronic_signing_message">I confirm that I\'ve followed the steps for this age verified delivery</string>
    <string name="avd_error_page_title">Don\\\'t Deliver</string>
    <string name="avd_error_page_heading">Don\\\'t deliver any packages</string>
    <string name="avd_error_page_message">The recipient does not meet the age requirements. Keep all of the packages and continue to your other deliveries.</string>
    <string name="avd_error_page_invalid_id_message">The recipient\\\'s ID is expired, and is no longer valid. Keep all of the packages and continue to your other deliveries.</string>
    <string name="avd_manual_entry_acknowledgment_title">Enter info as it appears on the recipient\\\'s ID</string>
    <string name="avd_manual_entry_acknowledgment_body">Entering information without seeing the ID or entering fake information will result in termination of the Agreement and you will no longer be able to deliver for Amazon.</string>
    <string name="avd_manual_entry_acknowledgment_confirm">Acknowledge</string>
    <string name="avd_agreement_title">Verify Age &amp;amp; Identity</string>
    <string name="avd_agreement_requirements">Requirements</string>
    <string name="avd_agreement_verify_id_and_age">Always check ID, &lt;b&gt;match the photo with the recipient, &lt;/b&gt;ensure the recipient is aged %1$d or over, and the ID is not expired.</string>
    <string name="avd_agreement_verify_id_and_age_texas">Always check ID, match the photo with the recipient, ensure the recipient is aged %1$d or over, and the ID is not expired.</string>
    <string name="avd_agreement_verify_signature">Ensure the &lt;b&gt;recipient signs for the order &lt;/b&gt;</string>
    <string name="avd_agreement_intoxication">Do not deliver to an intoxicated recipient</string>
    <string name="avd_agreement_verify_age">Only deliver to an adult who is older than %1$d</string>
    <string name="avd_agreement_verify_id">Always check ID and ensure it matches the recipient</string>
    <string name="avd_agreement_verify_id_under_25">Always check ID if the recipient appears to be under 25 years old</string>
    <string name="avd_agreement_verify_signature_and_location">Ensure the recipient signs for the order, do not leave in a safe place or with a neighbour</string>
    <string name="avd_agreement_verify_recipient_not_intoxicated">Recipient must not be intoxicated.</string>
    <string name="avd_agreement_checkbox">I acknowledge that complying with all the steps above is a legal requirement and must be correctly followed or it may result in no longer being able to deliver for Amazon.</string>
    <string name="avd_agreement_continue">Continue</string>
    <string name="avd_agreement_acknowledge">Acknowledge</string>
    <string name="delivery_recipient_group_delivery_customer_name">One of these customers</string>
    <string name="delivery_recipient_confirmation_subheader_text">Delivered to</string>
    <string name="delivery_recipient_doornumber_confirmation_text">%1$s at %2$s</string>
    <string name="delivery_recipient_name_edittext_hint">Enter first name</string>
    <string name="delivery_recipient_doornumber_edittext_hint">1234</string>
    <string name="delivery_customer_present_header">%1$s or a household member must be present to complete delivery</string>
    <string name="delivery_customer_present_body">If you are unable to hand the package directly to the customer, press \"Cancel\" and find a safe location to leave the package.</string>
    <string name="delivery_customer_present_acknowledge">Acknowledge &amp;amp; Continue</string>
    <string name="delivery_customer_present_cancel">Cancel</string>
    <string name="route_assignment_scan_title">Pick up a route for a %s block</string>
    <string name="route_progress_view_title">Work remaining</string>
    <string name="route_scan_guide_info_1">&lt;![CDATA[&lt;span&gt;Meet an Amazon Associate&lt;/span&gt;]]&gt; for your route code</string>
    <string name="route_scan_guide_info_2">&lt;![CDATA[&lt;span&gt;Locate the rack&lt;/span&gt;]]&gt; with your assigned route</string>
    <string name="route_scan_guide_info_3">&lt;![CDATA[&lt;span&gt;Scan the route code&lt;/span&gt;]]&gt; to see your route overview</string>
    <string name="route_scan_no_available_routes_message">If there are no routes, &lt;![CDATA[&lt;span&gt;tap here&lt;/span&gt;]]&gt; to get discharged. You must do so in order to get paid.</string>
    <string name="route_scan_button_text">Scan route code</string>
    <string name="route_scan_confirmation">You\\\'re all set to go</string>
    <string name="route_scan_title">Scan route code</string>
    <string name="route_scan_subtitle">Align the route code in the frame</string>
    <string name="route_scan_assigned_to_other_error_large">Someone is already assigned to this route</string>
    <string name="route_scan_assigned_to_other_error_small">Ask an Amazon associate for another route.</string>
    <string name="route_scan_length_mismatch_error_large">Route assignment does not match your block length</string>
    <string name="route_scan_length_mismatch_error_small">Ask an Amazon associate for a route for %s block.</string>
    <string name="route_reservation_service_type_mismatch_error_large">Route assignment does not match your vehicle type</string>
    <string name="route_reservation_service_type_mismatch_error_small">Ask an Amazon associate for an alternate route.</string>
    <string name="route_scan_getting_route">Getting your route…</string>
    <string name="route_scan_downloading_packages">Downloading packages…</string>
    <string name="package_verification_pickup">Manual pick up</string>
    <string name="package_express_pickup">Express pick up\n(No scan necessary)</string>
    <string name="package_complete_pickup">Complete pickup</string>
    <string name="route_assignment_view_packages">View Pick up</string>
    <string name="no_available_routes_title">You successfully checked out</string>
    <string name="no_available_routes_statement">You will be paid for this block and your reliability rating will not be affected.</string>
    <string name="gsf_aa_no_available_routes_confirmation">Continue</string>
    <string name="no_available_routes_confirmation">Okay</string>
    <string name="route_unassignment_notification">You were unassigned from your route</string>
    <string name="cancel_package_removal_confirmation_title">Cancel Package Removal\?</string>
    <string name="cancel_package_removal_confirmation_body">Canceling package removal will add these packages back to your pick up assignment.</string>
    <string name="cancel_package_removal_confirmation_positive_button">Cancel Removal</string>
    <string name="cancel_package_removal_confirmation_negative_button">Go Back</string>
    <string name="route_scan_success">Route Assigned!</string>
    <string name="discharge_qr_code_title">No route available</string>
    <string name="discharge_qr_code_guide_1">&lt;![CDATA[&lt;span&gt;Find an Amazon associate&lt;/span&gt;]]&gt;</string>
    <string name="discharge_qr_code_guide_2">&lt;![CDATA[&lt;span&gt;Show this QR code&lt;/span&gt;]]&gt; to an Amazon associate, who will scan and confirm your discharge</string>
    <string name="discharge_qr_code_transporter_email_id_not_available_message">We are unable to generate a QR code; please reach out to an Amazon associate for assistance.</string>
    <string name="itinerary_sync_error_alert_dialog_title">Unable to sync approvals</string>
    <string name="itinerary_sync_error_alert_dialog_message">Please try again or speak with an Amazon associate for help</string>
    <string name="itinerary_chip_shipper_container_pickup_text">Bags</string>
    <string name="swa_container_shipper_pickup_navi_title">This shipper uses bags</string>
    <string name="swa_container_shipper_pickup_navi_body">• Bring a dolly for moving large bags easily.\n• Take empty bags from your vehicle to drop off at the shipper.\n• Don\\\'t pick up any bag that is damaged or not properly sealed.</string>
    <string name="swa_container_shipper_pickup_navi_button_text">Got it</string>
    <string name="container_manual_entry_shipper_pickup_toolbar_title">Pick up</string>
    <string name="container_manual_entry_shipper_pickup_screen_title">How many bags did you pick up at this shipper\?</string>
    <string name="container_manual_entry_shipper_pickup_primary_button_text">Continue</string>
    <string name="container_manual_entry_shipper_drop_off_toolbar_title">Drop off</string>
    <string name="container_manual_entry_shipper_drop_off_screen_title">How many empty bags did you drop off\?</string>
    <string name="container_manual_entry_shipper_drop_off_warning_info_message">Please drop off %d empty bags. If you don\\\'t have that many, enter the number you dropped off.</string>
    <string name="container_manual_entry_shipper_drop_off_warning_error_message">Don\\\'t drop off more than %d empty bags</string>
    <string name="container_manual_entry_shipper_drop_off_primary_button_text">Continue</string>
    <string name="container_manual_entry_station_pickup_toolbar_title">Pick up</string>
    <string name="container_manual_entry_station_pickup_screen_title">How many empty bags did you pick up\?</string>
    <string name="container_manual_entry_station_pickup_warning_info_message">Make sure you have %d empty bags in your vehicle. If you can\\\'t, talk to an associate.</string>
    <string name="container_manual_entry_station_pickup_primary_button_text">Continue</string>
    <string name="container_manual_entry_input_hint">Example: %d</string>
    <string name="container_manual_entry_station_stop_navi_title_text">Pick up empty bags</string>
    <string name="container_manual_entry_station_stop_navi_main_paragraph_text">Pick up empty bags to drop off at the shippers tomorrow.\n\nIf there aren\\\'t enough empty bags to pick up, talk to an associate for help.</string>
    <string name="container_manual_entry_station_stop_navi_button_text">Got it</string>
    <string name="container_information_toolbar_title">Pick up</string>
    <string name="container_information_screen_title">Scan any packages that aren\\\'t in bags</string>
    <string name="container_information_intro">If there aren\\\'t any packages to scan:</string>
    <string name="container_information_first_paragraph_index">1.</string>
    <string name="container_information_first_paragraph_content">For security reasons, please ask the shipper to open any 1 bag, and scan 1 package inside. You don\\\'t need to scan any packages from other bags.</string>
    <string name="container_information_second_paragraph_index">2.</string>
    <string name="container_information_second_paragraph_content">Ask the shipper to reseal the bag.</string>
    <string name="container_information_primary_button_text">Scan packages</string>
    <string name="container_help_option_cube_out_title">My vehicle can\\\'t fit more items</string>
    <string name="container_help_option_cube_out_caption">A rescue will be sent to this address to pick up the remaining packages.</string>
    <string name="retrigger_otp_success_message">OTP has been sent to the customer\\\'s mobile number successfully</string>
    <string name="retrigger_otp_failure_message">There was an error retriggering OTP. Please try again.</string>
    <string name="route_scan_failure_with_info">Unable to fetch your route: %s</string>
    <string name="route_scan_backend_update_failed">Unable to fetch your route, please try again</string>
    <string name="scan_another_route_code">Scan another route code</string>
    <string name="route_scan_default_error_message_large">Something went wrong</string>
    <string name="route_scan_default_error_message_small">Please rescan the route QR code or ask an Amazon associate for help</string>
    <string name="no_scheduled_assignment_error_message">There was an issue retrieving your scheduled assignment. Please try again.</string>
    <string name="aa_activity_title">Auto-Assign</string>
    <string name="aa_matching_route">Matching you with a route… </string>
    <string name="aa_wait_header">Please wait while we prepare your route</string>
    <string name="aa_wait_body">Thank you for waiting - this shouldn\\\'t take more than 30 minutes. Watch the app for staging location and route # details.</string>
    <string name="aa_wait_for_route">PLEASE WAIT</string>
    <string name="aa_wait_go_to_pickup">Go to pickup area</string>
    <string name="aa_route_ready_header">Proceed to pickup</string>
    <string name="aa_route_ready_pickup_location">Pick up at %1$s</string>
    <string name="aa_route_ready_pickup_ready">Your route\\\'s packages are ready for pickup. Look for the signs in the station to find the staging area for your route.</string>
    <string name="aa_route_ready_pickup_scan_instructions">&lt;![CDATA[When you have arrived, &lt;b&gt;scan any package&lt;/b&gt; from the cart to confirm you are at the right place. You can then pick up your packages.]]&gt;</string>
    <string name="aa_confirm_scan_header_title">Confirm Route</string>
    <string name="aa_route_confirmed">Route confirmed</string>
    <string name="aa_confirm_scan_header_message">Scan any package at staging area %1$s. This confirms your route.</string>
    <string name="aa_route_delayed_header">Please see an Amazon associate</string>
    <string name="aa_route_delayed_body">Please check with an Amazon associate at the site for information about your route.</string>
    <string name="gsf_aa_no_available_routes_header">No routes available for now</string>
    <string name="gsf_aa_no_available_routes_title">PLEASE WAIT</string>
    <string name="gsf_aa_no_available_routes_body">It turns out there are no more routes available for this block. Rest assured you\\’ll still get paid.</string>
    <string name="gsf_aa_no_available_free_to_leave">You are free to leave when ready.</string>
    <string name="aa_no_available_routes_header">No available routes</string>
    <string name="aa_no_available_routes_body">Looks like there are no more available routes for today. Good news is that you still get paid.</string>
    <string name="aa_no_available_free_to_leave">You are free to leave now.</string>
    <string name="aa_route_unassign_looking_for_new_route">We are looking for a new route for you.</string>
    <string name="aa_route_unassign_route_unavailable">Your previous route is no longer available. Please wait while we match you with a new one.</string>
    <string name="aa_not_your_route">This is not your route</string>
    <string name="aa_route_not_assigned_to_you_body_1">Please confirm you are at %1$s or ask an Amazon Associate for help finding the right route.</string>
    <string name="aa_staging_area">Staging area</string>
    <string name="aa_route_number">Route #</string>
    <string name="aa_scan_confirm_button_text">Scan to confirm</string>
    <string name="aa_your_route_header">Your Route</string>
    <string name="aa_see_associate">See Amazon Associate</string>
    <string name="id_scan_dialog_title">Verify your driver\\\'s license now</string>
    <string name="id_scan_refresh_menu_option">Refresh</string>
    <string name="id_scan_gsf_step_1">&lt;b&gt;Find the designated kiosk&lt;/b&gt; at the delivery station.</string>
    <string name="id_scan_gsf_step_2">&lt;b&gt;Scan the barcode&lt;/b&gt; on the back of your license for verification.</string>
    <string name="id_scan_gsf_step_3">&lt;b&gt;Once verified,&lt;/b&gt; this screen will close, and you can begin pickup.</string>
    <string name="id_scan_gsf_step_4">&lt;b&gt;Find an Amazon associate&lt;/b&gt; if you experience any issues.</string>
    <string name="id_scan_amzl_step_1">&lt;b&gt;Have your driver\\\'s license&lt;/b&gt; ready to present to an Amazon associate.</string>
    <string name="id_scan_amzl_step_2">&lt;b&gt;An Amazon associate&lt;/b&gt; will scan your license for verification.</string>
    <string name="id_scan_amzl_step_3">&lt;b&gt;Once verified,&lt;/b&gt; this screen will close, and you can begin pickup.</string>
    <string name="id_scan_auto_assign_step3">&lt;b&gt;Once your identity is verified,&lt;/b&gt; you will be matched with a route.</string>
    <string name="route_overview_route">Route</string>
    <string name="route_overview_deliveries">Deliveries</string>
    <string name="unknown_pickups_info">Unknown number of pickups</string>
    <string name="route_overview_pickups">Pickups</string>
    <string name="route_overview_packages">Packages</string>
    <string name="route_overview_title">Route Overview</string>
    <string name="route_length_headline">%s Route</string>
    <string name="package_list_overview_activity">Package List Overview Activity</string>
    <string name="pickup_confirmation_instruction_text">Picked up from</string>
    <string name="unable_to_pickup_confirmation_instruction_text">Unable to pick up because</string>
    <string name="customer_return_pickup_confirmation_instruction_message">Someone at customer address</string>
    <string name="mfn_pickup_confirmation_instruction_message">Store representative</string>
    <string name="customer_return_do_not_pickup_package_instruction_text">Do not pickup package.</string>
    <string name="delivery_recipient_attended_subheader_text">Who is the recipient\?</string>
    <string name="delivery_recipient_customer">%s</string>
    <string name="delivery_recipient_household_member">%s\\\'s household member</string>
    <string name="delivery_recipient_receptionist">Receptionist or doorman</string>
    <string name="delivery_recipient_mailroom_clerk">Mailroom clerk</string>
    <string name="delivery_recipient_another_person">Another person, per customer\\\'s instructions</string>
    <string name="delivery_recipient_mailroom_attendant">Mailroom attendant</string>
    <string name="delivery_recipient_suggested_location_single">%s is the suggested location for this delivery</string>
    <string name="delivery_recipient_suggested_location_multiple">This is not the suggested delivery location</string>
    <string name="delivery_recipient_mail_room_attendant">Mail room attendant</string>
    <string name="delivery_recipient_preferred_location_single">%s is the preferred location for this delivery</string>
    <string name="delivery_recipient_preferred_location_multiple">This is not the preferred delivery location</string>
    <string name="delivery_recipient_more_options">More Options</string>
    <string name="delivery_recipient_other_as_instructed_amzl">Another person, per customer\\\'s instructions</string>
    <string name="delivery_recipient_unattended_subheader_text">Where are you leaving the package\?</string>
    <string name="delivery_recipient_secure_location">Another safe location</string>
    <string name="delivery_recipient_secure_mailroom">In a secure mailroom</string>
    <string name="delivery_recipient_another_person_customer_instruction">Another person, per customer\\\'s instructions</string>
    <string name="delivery_recipient_another_secure_location">Another safe location</string>
    <string name="additional_info_recipient_name">What\\\'s the recipient\\\'s name\?</string>
    <string name="additional_info_recipient_door_number">What is %s\\\'s house or apartment number\?</string>
    <string name="additional_info_neighbor_name">What\\\'s the neighbor\\\'s name\?</string>
    <string name="additional_info_customer_name">What\\\'s the customer\\\'s name\?</string>
    <string name="additional_info_receptionist_doorman">What\\\'s the receptionist/doorman\\\'s name\?</string>
    <string name="additional_info_another_person">What is the recipient\\\'s name\?</string>
    <string name="additional_info_mailroom_clerk">What\\\'s the mailroom clerk\\\'s name\?</string>
    <string name="additional_info_mailroom_attendant">What\\\'s the mailroom attendant\\\'s name\?</string>
    <string name="additional_info_security_guard_name">What\\\'s the security guard\\\'s name\?</string>
    <string name="additional_info_property_office">What\\\'s the property office clerk\\\'s name\?</string>
    <string name="additional_info_colleague_name">What\\\'s the colleague\\\'s name\?</string>
    <string name="swipe_to_finish_button_text">Swipe to finish</string>
    <string name="swipe_to_skip_scanning_text">Skip scanning</string>
    <string name="swipe_to_continue_text">Swipe to continue</string>
    <string name="swipe_to_signature_button_text">Swipe to Sign</string>
    <string name="manual_barcode_entry_title_activity">PACKAGE ID</string>
    <string name="manual_barcode_entry_confirm_button_text">Confirm</string>
    <string name="manual_barcode_error_short">Package ID should be at least 5 characters long</string>
    <string name="manual_barcode_entry_title_text">Enter package ID</string>
    <string name="manual_barcode_entry_hint_text">Example: 12345A123</string>
    <string name="eula_title_activity">LICENSE</string>
    <string name="eula_accept_button">Close</string>
    <string name="security_title_activity">SECURITY POLICY</string>
    <string name="security_setup_title">Set up security</string>
    <string name="security_setup_instructions">&lt;![CDATA[To protect customer information, this app needs your permission to:&lt;br&gt;&lt;br&gt;• Lock the device\\\'s screen after periods of inactivity&lt;br&gt;• Require a minimum %1$d-digit PIN, which you will set up, for unlocking the device.&lt;br&gt;&lt;br&gt;Tap below to review the security policy. You\\\'ll have to tap &lt;b&gt;Activate&lt;/b&gt; on the next screen to grant permission.]]&gt;</string>
    <string name="security_setup_button">Review security policy</string>
    <string name="security_pin_title">Set up PIN</string>
    <string name="security_pin_instructions">&lt;![CDATA[For security purposes, you need to set up a minimum %1$d-digit PIN. Any PIN previously set for this device will be replaced.&lt;br&gt;&lt;br&gt;After tapping &lt;b&gt;Continue&lt;/b&gt; below, do the following:&lt;br&gt;&lt;br&gt;• If you have one, enter your current PIN and tap &lt;b&gt;OK&lt;/b&gt;&lt;br&gt;• On the Screen unlock settings screen, tap &lt;b&gt;PIN&lt;/b&gt;&lt;br&gt;• Enter a new minimum %2$d-digit PIN and tap &lt;b&gt;OK&lt;/b&gt;&lt;br&gt;• Re-enter the new minimum %3$d-digit PIN and tap &lt;b&gt;OK&lt;/b&gt;&lt;br&gt;&lt;br&gt;Tap &lt;b&gt;Continue&lt;/b&gt; to set up your PIN.]]&gt;</string>
    <string name="security_pin_button">Continue</string>
    <string name="security_ok_title">Security acceptable</string>
    <string name="security_ok_instructions">Your device\\\'s credentials meet the requirements of this app. Tap &lt;b&gt;Continue&lt;/b&gt; to enter the app.</string>
    <string name="security_ok_button">Continue</string>
    <string name="security_credential_explanation">To protect customer information, the device must be secured to use the app.</string>
    <string name="reject_reason_title_activity">DELIVER</string>
    <string name="confirmation_by_image_title_activity">PHOTO</string>
    <string name="reject_reason_title_locked">PROBLEM</string>
    <string name="reject_reason_pickup_title">Why can\\\'t you pick up\?</string>
    <string name="reject_reason_deliver_title">Why can\\\'t you deliver\?</string>
    <string name="reject_reason_exchange_title_activity">EXCHANGE</string>
    <string name="reject_reason_exchange_title">Why can\\\'t you exchange\?</string>
    <string name="reject_reason_reason">Reason</string>
    <string name="reject_reason_spinner_default">Select reason</string>
    <string name="reject_reason_finish_button">Finish without delivering</string>
    <string name="reject_reason_wait_to_arrive">Wait until you get to the delivery location.</string>
    <string name="reject_reason_wait_to_arrive_exchange">Wait until you get to the exchange location.</string>
    <string name="reject_reason_make_attempt_message">Before marking this as undeliverable, try delivering it at the location.\nIf you can\\\'t reach the location, call Support and let them know.</string>
    <string documentation="New version of reject_reason_make_attempt_message" name="reject_reason_make_attempt_safety_message">You\\\'re too far from the delivery point to mark this package undeliverable in the app. If you can\\\'t safely get closer, please contact Driver Support.</string>
    <string name="reject_reason_continue_delivering">\@string/return_items_continue_delivering</string>
    <string name="reject_reason_unable_to_attempt">Unable to attempty delivery</string>
    <string name="work_schedule_title_activity">CALENDAR</string>
    <string name="availability_title_activity">AVAILABILITY</string>
    <string name="availability_workweek_title_activity">AVAILABILITY</string>
    <string name="account_title_activity">Settings</string>
    <string name="account_personal_info">Personal information</string>
    <string name="account_csp_phone_number">Device phone number</string>
    <string name="account_region">Region</string>
    <string name="account_log_upload">Usage data</string>
    <string name="account_legal_info">View legal information</string>
    <string name="account_app_version">Version info</string>
    <string name="account_management_home_vehicle_info">Vehicle information</string>
    <string name="account_transportation_mode_title">TRANSPORTATION METHOD</string>
    <string name="account_dsp_phone_number">Phone number</string>
    <string name="account_language">App language</string>
    <string name="account_transportation_mode">Transportation method</string>
    <string name="account_sign_out">Sign out</string>
    <string name="account_check_for_updates">Check for updates</string>
    <string name="account_clear_cache">Clear cache</string>
    <string name="account_clear_cache_message">Are you sure you want to clear app cache\?</string>
    <string name="account_clear_cache_confirm">Clear</string>
    <string name="account_onduty_error">Check for app updates when you\\\'re off duty.</string>
    <string name="account_latest_version_installed">Amazon Flex is up to date.</string>
    <string name="account_mobile_usage_text">We\\\'ll use this number to contact you while you\\\'re making deliveries.</string>
    <string name="account_submit">Submit</string>
    <string name="account_done">Done</string>
    <string name="account_transportation_mode_desc">What transportation method do you prefer\?</string>
    <string name="account_map_provider_desc">Select your preference</string>
    <string name="account_map_provider">Map Provider</string>
    <string name="account_offline_maps">Offline Maps</string>
    <string name="account_log_device_id">The device ID associated with logging:</string>
    <string name="account_log_no_device_id">No Device ID</string>
    <string name="account_log_upload_title">USAGE DATA</string>
    <string name="account_log_upload_enabled">Automatically send</string>
    <string name="account_log_upload_disabled">Don\\\'t send</string>
    <string name="account_log_upload_usage_text">Help us to improve our products and service by giving Amazon Flex permission to collect diagnostic and usage data.</string>
    <string name="account_enter_number">Enter number</string>
    <string name="map_provider_switch_to_mapbox_title">Offline maps is not supported with Mapbox.</string>
    <string name="map_provider_switch_to_mapbox_message">If you switch to Mapbox, you will not be able to use maps while offline.</string>
    <string name="map_provider_switch_to_mapbox_ok_button">Switch anyway</string>
    <string name="map_provider_switch_to_mapbox_cancel_button">Cancel</string>
    <string name="map_provider_offline_support_title">ATTENTION: Switch Maps\?</string>
    <string name="map_provider_offline_support_message">You need to switch to Maps 2.0 in order to use maps while offline.</string>
    <string name="release_notes_title">Version %s</string>
    <string name="release_notes_body_header">App improvements</string>
    <string name="release_notes_body_text_generic">We\\\'ve made a number of updates to increase the stability and performance of the app.</string>
    <string name="offer_preferences_fragment_title">Preferred Scheduling</string>
    <string name="legal_title_activity">LEGAL INFORMATION</string>
    <string name="legal_additional_terms_title">Additional Terms</string>
    <string name="legal_privacy_policy_title">Privacy Notice</string>
    <string name="legal_csp_tos_title">Independent Contractor Terms of Service</string>
    <string name="legal_dsp_tos_title">License Agreement and Terms of Use</string>
    <string name="legal_mfn_tos_title">License Agreement and Terms of Use</string>
    <string name="sync_syncing">Syncing…</string>
    <string name="sync_updating_phone_number">Updating phone number</string>
    <string name="call_dialog_title">Call</string>
    <string name="call_customer_text">Call %1$s\?</string>
    <string name="call_customer_message">Confirm below that you want to call the customer. Your phone will ring to connect you.</string>
    <string name="call_customer_in_progress_title">Calling customer</string>
    <string name="call_customer_in_progress">You will receive a call shortly</string>
    <string name="call_no_number_available">No phone number available for %1$s</string>
    <string name="call_customer_amazon_shipping_alert_box_generic_text">The customer may not have placed this order on Amazon. When you call, tell them that you\\\'re from Amazon Shipping and are delivering an order from another seller.</string>
    <string name="call_customer_amazon_shipping_alert_box_shipper_name_text">The customer may not have placed this order on Amazon. When you call, tell them that you\\\'re from Amazon Shipping and are delivering an order from %1$s.</string>
    <string name="text_customer_text">Text %1$s\?</string>
    <string name="scan_confirm_pickup_title">Scan</string>
    <string name="scan_confirm_pickup_header">Cancel pickup\?</string>
    <string name="scan_confirm_pickup_message">If you cancel your pickup, all items will be removed.</string>
    <string name="scan_confirm_pickup_accept_button">Cancel pickup</string>
    <string name="scan_confirm_pickup_cancel_button">Continue pickup</string>
    <string name="scan_confirm_delivery_title">Scan</string>
    <string name="scan_confirm_delivery_header">Cancel delivery\?</string>
    <string name="scan_confirm_delivery_message">If you cancel your delivery, all items will be removed.\n\nDo you want to cancel your delivery\?</string>
    <string name="scan_confirm_delivery_accept_button">Cancel delivery</string>
    <string name="scan_confirm_delivery_cancel_button">Continue delivery</string>
    <string name="scan_to_select">Scan to select</string>
    <string name="transfer_packages_title">Transfer packages</string>
    <string name="transfer_packages_message">These packages are assigned to another courier. Do you want to transfer them into your possession\?</string>
    <string name="transfer_packages_accept_button">Yes, transfer packages</string>
    <string name="transfer_packages_cancel_button">Cancel</string>
    <string name="transfer_packages_fragment_title">Transfer packages\?</string>
    <string name="transfer_packages_fragment_message">These packages were already assigned to another courier. Do you want to deliver them\?</string>
    <string name="transfer_packages_fragment_accept_button">Yes, I\\\'ll deliver them</string>
    <string name="transfer_packages_fragment_cancel_button">No, I\\\'ll leave them here</string>
    <string name="transfer_packages_fragment_action_bar_title">Transfer</string>
    <string name="app_policy_admin_name">Amazon Delivery Device Policy Admin</string>
    <string name="fetch_item_info_start">Checking for updates</string>
    <string name="fetch_item_info_progress">Checking for updates…</string>
    <string name="fetch_item_title_activity">CHECK UPDATES</string>
    <string name="apk_update_checking_latest_apk_version">Checking latest version of the app</string>
    <string name="apk_update_checking_latest_available_version">Checking latest APK version</string>
    <string name="apk_update_skip_update">Skip Update</string>
    <string name="apk_update_cleaning_up_files">Cleaning up files</string>
    <string name="apk_update_preparing_install">Preparing new version for install</string>
    <string name="apk_update_title_activity">UPDATE APP</string>
    <string name="file_update_title_activity">UPDATE FILE</string>
    <string name="scan_error_header_technical_error">There was a technical error</string>
    <string name="scan_error_header_already_picked_up">You have already picked up this package(s)</string>
    <string name="scan_error_header_already_assigned">You have already been assigned this return pickup(s)</string>
    <string name="scan_error_message_try_again">Please try scanning the package again.</string>
    <string name="scan_error_message_invalid_packages">There\\\'s a problem with the following package(s)</string>
    <string name="scan_error_message_shipper_canceled_packages">Leave the package with the shipper. Continue picking up any remaining packages</string>
    <string name="scan_error_message_shipper_invalid_pickup">Leave the package with the shipper.</string>
    <string name="refresh_stops_sequence_request_error_generic">Error getting stops sequence.</string>
    <string name="update_error_title">Update error</string>
    <string name="refresh_itinerary_failed_title">Unable to update your itinerary</string>
    <string name="refresh_itinerary_failed_body">Please go to the itinerary screen and tap refresh to download your itinerary</string>
    <string name="update_error_dismiss">Dismiss</string>
    <string name="tr_object_reason_code_unknown">---</string>
    <string name="tr_object_reason_code_none">None</string>
    <string name="tr_object_reason_code_delivered_customer">Delivered to customer</string>
    <string name="tr_object_reason_code_delivered_receptionist">Delivered to receptionist: %1$s</string>
    <string name="tr_object_reason_code_delivered_member">Delivered to household member: %1$s</string>
    <string name="tr_object_reason_code_delivered_mail_room">Delivered to mailroom: %1$s</string>
    <string name="tr_object_reason_code_delivered_other">Delivered to other: %1$s</string>
    <string name="tr_object_reason_code_delivered_to_post_office">Post office</string>
    <string name="tr_object_reason_code_delivered_doorstep">Front door/Front porch</string>
    <string name="tr_object_reason_code_delivered_doorstep_Door">Front Door/Front porch</string>
    <string name="tr_object_reason_code_address">Can\\\'t find address</string>
    <string name="tr_object_reason_code_no_secure_location">Nowhere safe to leave package</string>
    <string name="tr_object_reason_code_damaged">Package is damaged</string>
    <string name="tr_object_reason_code_inaccessible">Access problem</string>
    <string name="tr_object_reason_code_unavailable">Customer unavailable</string>
    <string name="tr_object_reason_code_closed">Business closed</string>
    <string name="tr_object_reason_code_age_requirement_failed">Recipient did not meet age requirements.</string>
    <string name="tr_object_reason_code_window">Delivery window timeout</string>
    <string name="tr_object_reason_code_late_delivery">Delivery will be too late</string>
    <string name="tr_object_reason_code_no_items_delivered">Package no longer wanted</string>
    <string name="tr_object_reason_code_object_missing">Package missing</string>
    <string name="tr_object_reason_code_merchant_unable_to_fulfill">Merchant can\\\'t fulfill order</string>
    <string name="tr_object_reason_code_items_missing">Package has missing items</string>
    <string name="tr_object_reason_code_items_damaged">Package has damaged items</string>
    <string name="tr_object_reason_code_items_incorrect">Package has incorrect items</string>
    <string name="tr_object_reason_code_merchant_running_late">Merchant running late</string>
    <string name="tr_object_reason_code_canceled">Order canceled</string>
    <string name="tr_object_reason_code_locker_full">Locker full</string>
    <string name="tr_object_reason_code_store">Store</string>
    <string name="tr_object_reason_code_locker_package_cannot_fit">Package can\\\'t fit in Locker</string>
    <string name="tr_object_reason_code_locker_ineligible">Package is ineligible for Locker delivery</string>
    <string name="tr_object_reason_code_colleague">Colleague</string>
    <string name="tr_object_reason_code_property_office">Property Office</string>
    <string name="tr_object_reason_code_leasing_office">Leasing office</string>
    <string name="tr_object_reason_code_loading_dock">Loading dock</string>
    <string name="tr_object_reason_code_pickup_point">Pickup Point</string>
    <string name="tr_object_reason_code_oversized">Package is oversized</string>
    <string name="tr_object_reason_code_overweight">Package is overweight</string>
    <string name="tr_object_reason_code_package_hazmat">Package falls under Hazmat category</string>
    <string name="pickup_selection_oversized_packaging_subheader">Which package(s) are oversize\?</string>
    <string name="pickup_selection_overweight_packaging_subheader">Which package(s) are overweight\?</string>
    <string name="pickup_selection_damaged_package_subheader">Which package(s) are damaged\?</string>
    <string name="pickup_selection_hazmat_package_subheader">Which package(s) fall under hazmat category\?</string>
    <string name="pickup_selection_unexpected_item_subheader">Which items are unexpected\?</string>
    <string name="tr_object_reason_code_wrong_imei_number">Wrong IMEI number</string>
    <string name="tr_object_reason_code_wrong_serial_number">Wrong serial number</string>
    <string name="tr_object_reason_code_no_charger">No charger</string>
    <string name="tr_object_reason_code_no_product_box">No product box</string>
    <string name="tr_object_reason_code_no_packaging">No packaging</string>
    <string name="tr_object_reason_code_no_price_tag">No price tag</string>
    <string name="tr_object_reason_code_verification_failure">Verification failure</string>
    <string name="tr_object_reason_code_otp_not_available">OTP not available</string>
    <string name="tr_object_reason_code_bag_id_mismatch">Bag Id Mismatch</string>
    <string name="tr_object_reason_code_partial_delivery">Partial Delivery</string>
    <string name="tr_object_reason_code_collected_by_customer">Collected by customer</string>
    <string name="tr_object_reason_code_package_not_ready">Package not ready</string>
    <string name="tr_object_reason_code_on_road">Package out for delivery</string>
    <string name="tr_object_reason_code_cancelled_new_address">Delivery cancelled</string>
    <string name="tr_object_reason_code_not_attempted">Not attempted</string>
    <string name="tr_object_reason_code_cubeout">Package too large</string>
    <string name="tr_object_reason_code_delivered_to_delivery_station">Delivered to station</string>
    <string name="tr_object_reason_code_received_at_wrong_station">Package at wrong station</string>
    <string name="tr_object_reason_code_lost">Package lost</string>
    <string name="tr_object_reason_code_replaced">Cancelled and replaced</string>
    <string name="tr_object_reason_code_cancelled_replan">Cancelled and replanned</string>
    <string name="tr_object_reason_code_cancelled_rescheduled">Cancelled and rescheduled</string>
    <string name="tr_object_reason_code_deliver_later">Reattempt</string>
    <string name="tr_object_reason_code_obsolete_plan">Package plan changed</string>
    <string name="tr_object_reason_code_customer_not_available">Customer not available</string>
    <string name="tr_object_reason_code_badweather_delivery">Missed delivery - Bad weather / Out of delivery time</string>
    <string name="tr_object_reason_code_badweather_pickup">Missed pickup - Bad weather / Out of pickup time</string>
    <string name="tr_object_reason_code_office_closed">Office closed</string>
    <string name="tr_object_reason_code_unable_to_locate_address">Unable to locate address</string>
    <string name="tr_object_reason_code_unable_to_contact_customer">Unable to contact customer</string>
    <string name="tr_object_reason_code_area_not_accessible">Area not accessible</string>
    <string name="tr_object_reason_code_time_change">Schedule date/time change</string>
    <string name="tr_object_reason_code_customer_or_household_member">%s or a household member</string>
    <string name="tr_object_reason_code_neighbor">Neighbor</string>
    <string name="tr_object_reason_code_neighbour">Neighbour</string>
    <string name="tr_object_reason_code_mailroom_attendant">Mailroom attendant</string>
    <string name="tr_object_reason_code_non_amazon_locker">Non-Amazon locker</string>
    <string name="tr_object_reason_code_locker">Locker</string>
    <string name="tr_object_reason_code_porch">Porch</string>
    <string name="tr_object_reason_code_garage">Garage</string>
    <string name="tr_object_reason_code_greenhouse">Greenhouse</string>
    <string name="tr_object_reason_code_garden">Garden</string>
    <string name="tr_object_reason_code_shed">Shed</string>
    <string name="tr_object_reason_code_behind_wheelie_bin">Behind wheelie bin</string>
    <string name="tr_object_reason_code_mail_slot">Mail slot</string>
    <string name="tr_object_reason_code_security_guard">Security Guard</string>
    <string name="tr_object_reason_code_security_desk">Security Desk</string>
    <string name="tr_object_reason_code_side_porch">Side porch</string>
    <string name="tr_object_reason_code_terrace">Terrace</string>
    <string name="tr_object_reason_code_home">Home</string>
    <string name="tr_object_reason_code_delivered_rear_door">Rear door/Rear porch</string>
    <string name="tr_object_reason_code_delivered_to_delivery_box">Delivery Box</string>
    <string name="tr_object_reason_code_missing_incorrect_access_code">Security Access Code Needed</string>
    <string name="tr_object_reason_code_customer_rejected">Rejected</string>
    <string name="tr_object_reason_code_age_verification_failed">Recipient didn\\\'t meet age requirements</string>
    <string name="tr_object_reason_code_rescheduled_by_customer">Customer requested future delivery date</string>
    <string name="tr_object_reason_code_customer_request_weekday">Customer requested weekday delivery</string>
    <string name="tr_object_reason_code_customer_request_weekend">Customer requested weekend delivery</string>
    <string name="tr_object_reason_code_out_of_delivery_time">Out of Delivery Time</string>
    <string name="tr_object_reason_code_payment_not_ready">Payment Not Ready</string>
    <string name="tr_object_reason_code_bad_weather">Delivery is delayed due to bad weather</string>
    <string name="tr_object_reason_code_customer_moved">Customer moved</string>
    <string name="tr_object_reason_code_out_of_delivery_area">Delivery is delayed due to package out of delivery area</string>
    <string name="tr_object_reason_code_inside_house">Inside House</string>
    <string name="tr_object_reason_code_inside_box">Inside Secure Box</string>
    <string name="tr_object_reason_code_failed_to_contact">Package is undeliverable because customer is not available at location</string>
    <string name="tr_object_reason_code_delivery_shortage">Pickup delayed: out of the delivery capability (delivery shortage)</string>
    <string name="tr_object_reason_code_locker_issue">Locker not working</string>
    <string name="tr_object_reason_code_front_door">Front Door/Front porch</string>
    <string name="tr_object_reason_code_gas_meter">Gas Meter</string>
    <string name="tr_object_reason_code_building_manager_room">Building Manager Room</string>
    <string name="tr_object_reason_code_bicycle_basket">Bicycle Basket</string>
    <string name="tr_object_reason_code_no_paperwork">Missing paperwork</string>
    <string name="tr_object_reason_code_creturn_out_of_delivery_time">C-return pickup will be too late</string>
    <string name="tr_object_reason_code_mfn_pickup_out_of_delivery_time">MFN pickup will be too late</string>
    <string name="tr_object_reason_code_creturn_out_of_pickup_area">C-return pickup out of pickup area</string>
    <string name="tr_object_reason_code_merchant_out_of_pickup_area">MFN pickup out of pickup area</string>
    <string name="tr_object_reason_code_creturn_tr_canceled">C-return pickup canceled by customer</string>
    <string name="tr_object_reason_code_creturn_pickup_notready">C-return pickup is not ready or is missing</string>
    <string name="tr_object_reason_code_merchant_unavailable">Representative not available</string>
    <string name="tr_object_reason_code_merchant_cancel">MFN pickup canceled by seller</string>
    <string name="tr_object_reason_code_customer_cancel">Customer canceled pickup</string>
    <string name="tr_object_reason_code_other">Other reason</string>
    <string name="tr_object_reason_code_not_ready">Package is not ready or is missing</string>
    <string name="tr_object_reason_code_parcel_not_ready">Parcel is not ready or is missing</string>
    <string name="tr_object_reason_code_store_packages_not_ready">Package is not ready or is missing</string>
    <string name="tr_object_reason_code_customer_packages_not_ready">Package is not ready or is missing</string>
    <string documentation="en_IN 'should be parcel not ready', everything else should follow tr_object_reason_code_customer_packages_not_ready" name="tr_object_reason_code_customer_packages_not_ready_creturn">Package is not ready or is missing</string>
    <string name="tr_object_reason_code_poor_packaging">Items are poorly packaged</string>
    <string name="tr_object_reason_code_out_of_pickup_area">Out of pickup area</string>
    <string name="tr_object_reason_code_creturn_pickup_bad_weather">Pickup is delayed due to bad weather</string>
    <string name="tr_object_reason_code_mfn_pickup_bad_weather">Pickup is delayed due to bad weather</string>
    <string name="tr_object_reason_code_creturn_rescheduled">Customer requested future pickup date for C-return pickup</string>
    <string name="tr_object_reason_code_mfn_rescheduled">MFN pickup requested future pickup date</string>
    <string name="tr_object_reason_code_creturn_customer_unavailable">Customer unavailable for C-return pickup</string>
    <string name="tr_object_reason_code_mfn_customer_unavailable">Seller unavailable for MFN pickup</string>
    <string name="tr_object_reason_code_creturn_delivery_shortage">Pickup delayed: out of the delivery capability (delivery shortage)</string>
    <string name="tr_object_pickup_code_access_problem">Access problem for MFN or C-return pickup</string>
    <string name="tr_object_pickup_code_creturn_access_problem">Access problem for MFN or C-return pickup</string>
    <string name="tr_object_pickup_code_cant_find_address">Can\\\'t find address for MFN or C-return pickup</string>
    <string name="tr_object_pickup_code_business_closed">Business closed</string>
    <string name="tr_object_pickup_failed_to_contact">Package cannot be picked up because customer is not available at location</string>
    <string name="tr_object_pickup_by_customer">Customer picked up</string>
    <string name="tr_object_pickup_inaccessible">Access problem</string>
    <string name="tr_object_reason_code_creturn_pickup_canceled">Pickup cancelled</string>
    <string name="tr_object_reason_code_creturn_parcel_not_ready">Parcel not ready</string>
    <string name="tr_object_reason_code_mfn_seller_delayed_pickup">Seller delayed pickup</string>
    <string name="tr_object_reason_code_mfn_packaging_issue">Label or packaging issue</string>
    <string name="onroad_alert_activity">Request Rescue</string>
    <string name="tr_object_exchange_item_not_ready">Item is not ready for pickup</string>
    <string name="tr_object_exchange_item_doesnt_match">Item for pickup doesn\\\'t match the exchange details</string>
    <string name="stop_level_place_holder">Customer</string>
    <string name="notification_ack_button">Acknowledge</string>
    <string name="switch_devices_title">Switch devices\?</string>
    <string name="switch_devices_message">You\\\'re already signed in on another device. Use this device instead\?</string>
    <string name="switch_devices_accept_button">Yes, Use This Device</string>
    <string name="switch_devices_cancel_button">No, Sign Out</string>
    <string name="switch_devices_network_failure_message">Connect to WiFi or your cellular network and try again.</string>
    <string name="switch_devices_failed_message">Update Failed. Please try again.</string>
    <string name="title_activity_sample_maps">SampleMapsActivity</string>
    <string name="onboarding_contact_us_label">Contact Us</string>
    <string name="healthcare_subsidy_contact_us_label">Contact Us</string>
    <string name="healthcare_contact_us_text">Email us if you have questions about qualifying for a healthcare subsidy.</string>
    <string name="onboarding_loading_text">Loading…</string>
    <string name="onboarding_sign_out_loading_text">Signing Out…</string>
    <string name="info_button_ok">OK</string>
    <string name="onboarding_sign_out_header">Sign Out</string>
    <string name="onboarding_sign_out_message">Are you sure you want to sign out\?</string>
    <string name="onboarding_sign_out_primary_button">Sign out</string>
    <string name="onboarding_sign_out_secondary_button">Cancel</string>
    <string name="onboarding_load_data_header">An error occurred</string>
    <string name="onboarding_load_data_message">We were unable to load this screen due to a technical error. Please try again later.</string>
    <string name="onboarding_submit_data_message">We were unable to process this request due to a technical error. Please try again later.</string>
    <string name="onboarding_splash_header_complete_with_name">You are in business, %1$s!</string>
    <string name="onboarding_splash_header_complete_without_name">You are in business!</string>
    <string name="onboarding_splash_complete_body">Start delivering and making money.</string>
    <string name="calendar_setup_availability_header">Set up your availability</string>
    <string name="calendar_setup_availability_button">Set up availability</string>
    <string name="calendar_unable_display_availability_header">There\\\'s a problem showing availability. Check back later.</string>
    <string name="calendar_unable_display_availability_body">Your availability hasn\\\'t changed, but there was a technical error showing it. We\\\'re working to fix the issue.</string>
    <string name="availability_as_much_as_possible">As much as possible</string>
    <string name="availability_select_time">Select time</string>
    <string name="availability_not_set">Not set</string>
    <string name="availability_done">Done</string>
    <string name="availability_hour_range_header">Hour range %d</string>
    <string name="empty_availability_header">Availability not set</string>
    <string name="empty_availability_text">Tap &lt;b&gt;Add an hour range&lt;/b&gt; to set your preferences. You can set up to five hour ranges per day.</string>
    <string name="set_availability_begin">Arrive by</string>
    <string name="set_availability_end">Finish by</string>
    <string name="set_availability_button_label">Set availability</string>
    <string name="save_availability_button_label">Save availability</string>
    <string name="add_hour_range_button_label">Add an hour range</string>
    <string name="delete_availability_button_label">Delete</string>
    <string name="unset_availability_button_label">Not available on %1$s</string>
    <string name="calendar_detail_turn_off_availability">Turn off availability</string>
    <string name="calendar_detail_not_available">I\\\'m not available this time</string>
    <string name="calendar_detail_forfeit_time">Forfeit block</string>
    <string name="calendar_detail_forfeit_dialog">Are you sure you want to forfeit this block\?</string>
    <string name="calendar_detail_forfeit_or_transfer_dialog">Are you sure you want to forfeit this block\? You can also transfer it to another delivery partner. %1$s</string>
    <string name="calendar_detail_transfer_block">Transfer block</string>
    <string name="calendar_detail_forfeit_dialog_positive">Forfeit</string>
    <string name="calendar_detail_forfeit_dialog_negative">Don\\\'t forfeit</string>
    <string name="calendar_detail_forfeit_success">You forfeited the block.</string>
    <string name="calendar_detail_schedule_start_end_time_label">Start and end time</string>
    <string name="calendar_detail_schedule_start_time_label">Start</string>
    <string name="calendar_detail_schedule_end_time_label">End by</string>
    <string name="calendar_detail_schedule_disclaimer_label">* Please contact your manager for exact time.</string>
    <string name="calendar_detail_availability_label">Availability</string>
    <string name="calendar_detail_pay_label">Pay</string>
    <string name="calendar_detail_pay_amount">%s + Tips</string>
    <string name="calendar_detail_area">Area</string>
    <string name="calendar_detail_location_header">Location and other information</string>
    <string name="calendar_detail_start_location_header">Start location</string>
    <string name="calendar_detail_station_location_header">Station location</string>
    <string name="calendar_detail_station_location_text_india">Please go to your pickup point as informed by your supervisor</string>
    <string name="calendar_detail_starting_address_unavailable">Your pickup location will be available by %1$s</string>
    <string name="calendar_detail_starting_address_unavailable_dsp">Your station location will be available by %1$s</string>
    <string name="calendar_detail_starting_address_unavailable_csp">Your start location will be available by %1$s</string>
    <string name="calendar_detail_location_info">%1$s — %2$s</string>
    <string name="calendar_detail_congestion_zone">Congestion Zone</string>
    <string name="calendar_detail_vehicle_header">Required vehicle</string>
    <string name="calendar_detail_large_vehicle">Large</string>
    <string name="calendar_detail_starting_address_info">We\\\'ll send you your starting address at least 1 hour before your scheduled time begins</string>
    <string name="calendar_detail_late_forfeit_warning_dialog">This block will be canceled late, which may affect your standing.</string>
    <string name="calendar_detail_late_forfeit_warning_dialog_positive">Forfeit</string>
    <string name="calendar_detail_late_forfeit_warning_dialog_negative">Don\\\'t forfeit</string>
    <string name="work_selection_estimate_amount">%1$s-%2$s</string>
    <string name="address_feedback_add_hours">Add Hours</string>
    <string name="address_feedback_access_code">Access Code</string>
    <string name="address_feedback_add_access_code">Add Access Code</string>
    <string name="address_feedback_number_sign">#</string>
    <string name="address_feedback_edit">Edit</string>
    <string name="address_detail_time_text">Completed at %1$s</string>
    <string name="address_detail_attempted">Attempted</string>
    <string name="odometer_activity_title">MILEAGE</string>
    <string name="odometer_hint">ex. 24,901</string>
    <string name="odometer_go_on_duty">Go On Duty</string>
    <string name="odometer_go_off_duty">Go Off Duty</string>
    <string name="odometer_subtitle">Enter the mileage on your odometer</string>
    <string name="transportation_mode_activity_title">START</string>
    <string name="transportation_mode_subtitle">How are you delivering\?</string>
    <string name="transportation_mode_remember_checkbox_text">Remember this</string>
    <string name="transportation_mode_continue_button">Continue</string>
    <string name="transportation_mode_go_on_duty_button">Go On Duty</string>
    <string name="transportation_mode_instructions_title_text">You can change your mode of transportation from your account page.</string>
    <string name="dsp_ready_to_deliver_button">Ready to deliver</string>
    <string name="dsp_go_on_duty_button">Go On Duty</string>
    <string name="dsp_home_go_off_duty">Go Off Duty</string>
    <string name="dsp_home_continue_delivering">Continue Delivering</string>
    <string name="dsp_home_stay_on_duty_and_continue">Stay On Duty And Continue</string>
    <string name="dsp_home_no_stops_instruction_text">You have completed all of your stops.</string>
    <string name="dsp_return_activity_title">RETURN</string>
    <string name="dsp_return_swipe_to_arrive">Swipe to arrive</string>
    <string name="dsp_return_to_station_title">Return to the station</string>
    <string name="dsp_return_to_station_instruction">Go back to the station to finish. When you arrive, swipe below.</string>
    <string name="earnings_activity_title">EARNINGS</string>
    <string name="earnings_tips_pending">Tips pending</string>
    <string name="earnings_tips_included">Tips included</string>
    <string name="earnings_deposit_label">%1$s %2$s</string>
    <string name="earnings_deposit_label_strong">Deposit,</string>
    <string name="earnings_adjustment_label">Adjustment</string>
    <string name="earnings_adjustment_label_with_reason">Adjustment: %1$s</string>
    <string name="earnings_payment_pending">Payment pending</string>
    <string name="earnings_base_amount">Base %1$s</string>
    <string name="earnings_tips_amount">Tips %1$s</string>
    <string name="earnings_total_amount">Total %1$s</string>
    <string name="earnings_base_plus_tips">%1$s + tips</string>
    <string name="earnings_base_plus_tips_in_braces">(%1$s + tips)</string>
    <string name="earnings_tips_and_payment_pending">Tips and payment pending</string>
    <string name="earnings_payment_sent">Payment sent on %1$s</string>
    <string name="earnings_short_checking_account">Checking account ending in %1$s</string>
    <string name="earnings_short_savings_account">Savings account ending in %1$s</string>
    <string name="earnings_summary_no_data">You don\\\'t have any earnings yet. Go to &lt;b&gt;Menu&lt;/b&gt; &amp;gt; &lt;b&gt;Calendar&lt;/b&gt; to select when you\\\'re able to deliver. Then check &lt;b&gt;Offers&lt;/b&gt; to accept blocks. Your earnings will appear here after you have completed a delivery block.</string>
    <string name="earnings_summary_data_retrieval_failed"> There\\\'s a problem retrieving your earnings, please try again later. If the problem continues, go to &lt;b&gt;Help&lt;/b&gt; to contact &lt;b&gt;Support&lt;/b&gt;.</string>
    <string name="earnings_detail_service_provided_title">Earnings Details</string>
    <string name="earnings_detail_amount_label">Amount</string>
    <string name="earnings_detail_base_label">Base</string>
    <string name="earnings_detail_tips_label">Tips</string>
    <string name="earnings_detail_total_label">Total</string>
    <string name="earnings_detail_tips_settled">Including all tips</string>
    <string name="earnings_detail_tips_pending">Tips being processed</string>
    <string name="earnings_detail_payment_estimated_deposit_footer">Payment will be sent to your bank account by %1$s.</string>
    <string name="earnings_detail_payment_actual_deposit_footer">Payment was sent to your bank account on %1$s.</string>
    <string name="earnings_detail_adjustment_title">Adjustment Details</string>
    <string name="earnings_detail_adjustment_label">Adjustment</string>
    <string name="earnings_detail_deposit_title">Deposit Details</string>
    <string name="earnings_detail_deposit_date_label">Payment sent</string>
    <string name="earnings_detail_deposit_amount_label">Deposit</string>
    <string name="earnings_detail_deposit_delivery_range_label">For deliveries and adjustments made</string>
    <string name="earnings_detail_deposit_tips_range_label">Includes tips received</string>
    <string name="earnings_detail_deposit_disclaimer_next_day">Payments typically appear in your bank account on the next business day.</string>
    <string name="earnings_detail_deposit_disclaimer">It may take up to five business days for payments to appear in your account.</string>
    <string name="earnings_detail_report_problem">Report a problem</string>
    <string name="earnings_detail_report_problem_content">Hello,\n\nThank you for contacting Amazon Flex Support. In order to resolve your concern as quickly as possible, please fill out the information below.\n\nDate of block:\nTime of block:\nDescribe your question or concern here:\n\nYou may expect a reply via email within the next few days.\n\nThank you,\n\nAmazon Flex Support</string>
    <string name="earnings_detail_report_problem_subject">Payment Problems:%1$s</string>
    <string name="earnings_detail_report_problem_intent_chooser_instruction">Choose a client…</string>
    <string name="camera_viewfinder_helper">Scan</string>
    <string name="start_scanning_mesage">Scan a package to start</string>
    <string name="all_packages_scanned_message">All packages scanned</string>
    <string name="itinerary_map_view">Map</string>
    <string name="itinerary_list_view">List</string>
    <string name="itinerary_title_activity">ITINERARY</string>
    <string name="itinerary_title_route_overview">OVERVIEW</string>
    <string name="itinerary_refresh">Refresh</string>
    <string name="itinerary_continue">After you\\\'re assigned more stops, you\\\'ll see them in your itinerary.</string>
    <string name="itinerary_order_canceled">Do not deliver</string>
    <string name="itinerary_stop_canceled">Stop canceled at %1$s</string>
    <string name="itinerary_stop_canceled_no_timestamp">Stop canceled</string>
    <string name="itinerary_rush">Priority</string>
    <string name="itinerary_late">Late</string>
    <string name="itinerary_next_stop">Next Stop:</string>
    <string name="itinerary_current_stop">Current Stop:</string>
    <string name="itinerary_completed">Completed at %1$s</string>
    <string name="itinerary_completed_no_timestamp">Completed</string>
    <string name="itinerary_attempted">Attempted at %1$s</string>
    <string name="itinerary_attempted_no_timestamp">Attempted</string>
    <string name="itinerary_picked_up_at">Picked up at %1$s</string>
    <string name="itinerary_picked_up_at_no_timestamp">Picked up</string>
    <string name="itinerary_picked_up_with_exceptions">Picked up with exceptions at %1$s</string>
    <string name="itinerary_picked_up_with_exceptions_no_timestamp">Picked up with exceptions</string>
    <string name="itinerary_delivered_with_exceptions">Delivered with exceptions at %1$s</string>
    <string name="itinerary_delivered_with_exceptions_no_timestamp">Delivered with exceptions</string>
    <string name="itinerary_delivered_at">Delivered at %1$s</string>
    <string name="itinerary_delivered_at_no_timestamp">Delivered</string>
    <string name="itinerary_exchanged_at">Exchanged at %1$s</string>
    <string name="itinerary_exchanged_at_no_timestamp">Exchanged</string>
    <string name="itinerary_delivered_without_exchange_at">Delivered without exchange at %1$s</string>
    <string name="itinerary_delivered_without_exchange_at_no_timestamp">Delivered without exchange</string>
    <string name="itinerary_arrived_at">Arrived at %1$s</string>
    <string name="itinerary_arrived_at_no_timestamp">Arrived</string>
    <string name="itinerary_start_first_delivery_button_text">Start First Delivery</string>
    <string name="itinerary_review_itinerary_button_text">Review Itinerary</string>
    <string name="itinerary_return_to">Return to</string>
    <string name="itinerary_waypoint">Waiting area</string>
    <string name="itinerary_header_text"># %1$s • %2$s</string>
    <string name="itinerary_parcel_summary">Packages</string>
    <string name="itinerary_cod_summary">Transactions</string>
    <string name="itinerary_cod_cash_on_hand">Cash on hand</string>
    <string name="itinerary_cod_cash_by_card">Cash on card</string>
    <string name="itinerary_cod_cash_by_card_or_paylink">Cash on card or paylink</string>
    <string name="itinerary_cod_cash_by_paylink">Cash on paylink</string>
    <string name="itinerary_cod_upcoming">Upcoming transactions</string>
    <string name="itinerary_problems">Problems</string>
    <string name="itinerary_ewb_packages">E-way bill packages</string>
    <string name="itinerary_view">View</string>
    <string name="itinerary_to_deliver">To deliver or drop off</string>
    <string name="itinerary_to_pick_up">To pick up</string>
    <string name="itinerary_summary_view">Summary</string>
    <string name="itinerary_cod_total">Total</string>
    <string name="itinerary_collect_payment">Collect payment on delivery</string>
    <string name="itinerary_payment_collected_in_cash">Payment collected in cash</string>
    <string name="itinerary_payment_collected_by_card">Payment collected on card</string>
    <string name="itinerary_payment_collected_in_card_or_paylink">Payment collected in card or paylink</string>
    <string name="itinerary_payment_collected_by_paylink">Payment collected on paylink</string>
    <string name="itinerary_summary_weight">Weight</string>
    <string name="itinerary_summary_weight_header">Total Weight by Unit</string>
    <string name="itinerary_summary_bottle_deposit">Bottle Deposit</string>
    <string name="itinerary_summary_bottles_to_return">Bottles to return</string>
    <string name="itinerary_summary_crates_to_return">Crates to return</string>
    <string name="itinerary_summary_stops_header">Stops</string>
    <string name="itinerary_summary_stops_successful">Successful</string>
    <string name="itinerary_summary_stops_successful_header">Successful Stops</string>
    <string name="itinerary_summary_stops_problems">Problems</string>
    <string name="itinerary_summary_stops_to_do">To Do</string>
    <string name="itinerary_summary_stops_to_do_header">Stops To Do</string>
    <string name="itinerary_summary_stops_problems_header">Stop Problems</string>
    <string name="itinerary_summary_stops_problems_reattempt">To Re-attempt</string>
    <string name="itinerary_summary_stops_problems_unable_to_reattempt">Unable to Re-attempt</string>
    <string name="itinerary_summary_stops_view_all">View All</string>
    <string name="itinerary_summary_stops_problems_in_app_verification_failed">Item verification failed</string>
    <string name="itinerary_summary_stops_successful_pickup">Picked Up</string>
    <string name="itinerary_summary_stops_successful_deliver">Delivered</string>
    <string name="itinerary_summary_stops_successful_exchange">Exchanged</string>
    <string name="itinerary_summary_stops_successful_creturn">Picked Up from Customer</string>
    <string name="itinerary_summary_stops_successful_mfn_pickup">Picked Up from Merchant</string>
    <string name="itinerary_summary_stops_successful_bulk_delivery">Delivered at Station</string>
    <string name="itinerary_summary_stops_successful_commingled">Combined</string>
    <string name="itinerary_summary_stops_todo_pickup">Pickup</string>
    <string name="itinerary_summary_stops_todo_deliver">Deliver</string>
    <string name="itinerary_summary_stops_todo_exchange">Exchange</string>
    <string name="itinerary_summary_stops_todo_commingled">Combined</string>
    <string name="itinerary_summary_stops_todo_bulk_delivery">Deliver at Station</string>
    <string name="itinerary_summary_stops_todo_creturn">Customer Return Pickup</string>
    <string name="itinerary_summary_stops_todo_mfn_pickup">Merchant Pickup</string>
    <string name="itinerary_summary_stops_stop_count_package_count">%1$s, %2$s</string>
    <string name="itinerary_summary_stops_commingled">%1$s • %2$s</string>
    <string name="itinerary_summary_stop_info_popup_title">About stops and locations</string>
    <string name="itinerary_summary_stop_info_popup_body">Stops are where you park your vehicle.\nLocations are where you deliver packages.\n\nThe numbers may change if you\'re unable to deliver to certain locations, such as a locker, and need to deliver somewhere else, such as customer doorsteps. This has already been factored into your route time.</string>
    <string name="itinerary_skip_stops_dialog_title">Travel to this stop\?</string>
    <string name="itinerary_skip_stops_dialog_body">Changing your assigned route may make you late for other stops. You\\\'ll still need to attempt all stops on your route.</string>
    <string name="itinerary_skip_stops_dialog_positive">Travel to stop</string>
    <string name="itinerary_skip_stops_dialog_negative">Cancel</string>
    <string name="itinerary_no_skip_stops_dialog_text">Don\\\'t show this again</string>
    <string name="itinerary_chip_locker_text">Locker</string>
    <string name="itinerary_chip_non_amazon_locker_text">Non-Amazon locker</string>
    <string name="itinerary_chip_local_rush_retail">Store</string>
    <string name="itinerary_chip_in_home_text">In-home delivery</string>
    <string name="itinerary_chip_garage_text">Garage</string>
    <string name="itinerary_chip_box_text">Secure Box</string>
    <string name="itinerary_chip_otp_delivery_text">One-time password delivery</string>
    <string name="itinerary_chip_partial_delivery_text">Pantry</string>
    <string name="itinerary_chip_cash_load_text">Cash load</string>
    <string name="itinerary_chip_kyc_enabled_text">KYC Verification</string>
    <string name="itinerary_chip_station_text">Station</string>
    <string name="itinerary_delivery_service_text">Delivery services</string>
    <string name="itinerary_chip_return_bottles_text">Return items</string>
    <string name="itinerary_chip_amazon_shipping_text">Amazon Shipping</string>
    <string name="itinerary_chip_counter_shadow_mode_text">Counter</string>
    <string name="itinerary_search_match_scannable_id_title">Package Barcode</string>
    <string name="itinerary_search_hint_title">Try searching for</string>
    <string name="itinerary_search_hint_text">• Tracking ID</string>
    <string name="itinerary_search_typing_title">Continue typing</string>
    <string name="itinerary_search_typing_text">Keep typing to narrow down the list.</string>
    <string name="itinerary_search_no_result_title">No results</string>
    <string name="itinerary_search_no_result_text">Your search found no matches. Check your search request or contact Support if you think there\\\'s a problem.</string>
    <string name="itinerary_search_scanner_overlay_continue">Continue</string>
    <string name="itinerary_search_scanner_start_instructions">Scan a package barcode to select the corresponding stop.</string>
    <string name="access_code_instruction">What is the access code\?</string>
    <string name="itinerary_high_volume_dialog_title">Your planned first stop is a high-volume stop</string>
    <string name="itinerary_high_volume_dialog_body">Completing this stop as your first of the day will free up more room to work in your vehicle.</string>
    <string name="itinerary_high_volume_dialog_continue">Continue to selected stop</string>
    <string name="itinerary_high_volume_dialog_change_stop">Start with stop 1</string>
    <string name="access_code_hint">ex.#1234</string>
    <string name="access_code_button">Save access code</string>
    <string name="access_code_skip_button">SKIP</string>
    <string name="access_code_title_activity">ACCESS CODE</string>
    <string name="dismiss_menu_title">Dismiss</string>
    <string name="onboarding_alert_title">Thanks for your interest</string>
    <string name="onboarding_alert_text">Amazon Flex is not currently available in your country.</string>
    <string name="onboarding_alert_button">CLOSE APP</string>
    <string name="onboarding_contactus_title">CONTACT US</string>
    <string name="onboarding_contactus_subject">Subject</string>
    <string name="onboarding_contactus_submit_button_label">Submit</string>
    <string name="onboarding_contactus_subject_prompt">Choose a subject</string>
    <string name="onboarding_contactus_content_hint">ex. I put in the wrong expiration date, how can I correct this\?</string>
    <string name="choose_email_client">Choose an app to send email</string>
    <string name="start_work">Start Work</string>
    <string name="end_work">End Work</string>
    <string name="start_delivery">Start</string>
    <string name="home_continue_delivering">Continue delivering</string>
    <string name="home_continue_pickup">Continue picking up</string>
    <string name="home_continue_waiting">Continue waiting</string>
    <string name="home_continue_check_in">Continue checking in</string>
    <string name="go_to_start_location">Go to start location</string>
    <string name="start_schedule">Update my Availability</string>
    <string name="no_delivery_today">You don\\\'t have any delivery blocks scheduled today</string>
    <string name="schedule_open_blocks">Schedule for today</string>
    <string name="get_ready_to_deliver">Get ready to start your scheduled deliveries now.</string>
    <string name="lost_network_connection">You\\\'re offline. Connect to WiFi or your cellular network to continue.</string>
    <string name="request_failed">Sorry, something went wrong</string>
    <string name="home_delivering_now_title">You\\\'ll finish your current deliveries by %1$s.</string>
    <string name="home_well_done">Well done!</string>
    <string name="home_completed_schedule_today">You\\\'ve completed your current deliveries. Earn extra $ by scheduling more for today.</string>
    <string name="return_reminders_card_deadline">Due by %1$s on %2$s</string>
    <string name="home_go_to_email">Go to email</string>
    <string name="home_open_task_center">Open Notification Center</string>
    <string name="home_inactive_csp_instructions_title">Please Check Your Email</string>
    <string name="home_inactive_csp_instructions_title_task_center">Please Check Your Notification Center</string>
    <string name="home_inactive_csp_instructions_content">Your account is temporarily disabled. Check your email for instructions on how to update your account and resume making deliveries.</string>
    <string name="home_inactive_csp_instructions_content_task_center">Your account is temporarily disabled. Check your notification center to complete required tasks so that you can resume making deliveries.</string>
    <string name="home_inactive_csp_email_title">Email</string>
    <string name="home_today">Today</string>
    <string name="home_late_message">You\\\'ve missed your scheduled deliveries. Don\\\'t go to the start location.</string>
    <string name="home_late_message_dsp">You missed your scheduled block. Please contact an operator.</string>
    <string name="home_missed_schedule_disclaimer_label">No longer available</string>
    <string name="home_late_schedule_more">If you\\\'re still free today, you can check for other available delivery blocks.</string>
    <string name="home_screen_sync_failed_title">PLACEHOLDER: Sync Failed Title</string>
    <string name="home_screen_sync_failed_network_message">PLACEHOLDER: There was a problem connecting to the service. Try again in a few moments.</string>
    <string name="home_screen_sync_failed_state_sync_message">PLACEHOLDER: There was a problem with data. Try again in a few moments.</string>
    <string name="home_screen_sync_failed_button">Retry Sync</string>
    <string name="home_screen_sync_failed_cancel">Cancel</string>
    <string name="scheduling_refresh_offers">Refresh</string>
    <string name="scheduling_just_for_you">Reserved</string>
    <string name="scheduling_expiry_text">Accept by %1$s</string>
    <string name="scheduling_duration_label">Duration</string>
    <string name="scheduling_pay_label">Pay</string>
    <string name="scheduling_includes_tips">Includes Tips</string>
    <string name="scheduling_work">You\\\'re scheduled to work at this time</string>
    <string name="scheduling_decline">Decline block</string>
    <string name="scheduling_accept">Swipe to Schedule</string>
    <string name="scheduling_forfeit">Forfeit</string>
    <string name="scheduling_time_range">%1$s - %2$s</string>
    <string name="scheduling_time_and_cutoff">%1$s • (%2$s)</string>
    <string name="scheduling_price_range">%1$s - %2$s</string>
    <string name="scheduling_location_label">Location and other information</string>
    <string name="scheduling_starting_address_unavailable">Your pickup location will be available by %1$s</string>
    <string name="scheduling_detail_location_info">%1$s — %2$s</string>
    <string name="scheduling_detail_congestion_zone">Congestion Zone</string>
    <string name="scheduling_detail_vehicle_header">Required vehicle</string>
    <string name="scheduling_detail_large_vehicle">Large</string>
    <string name="scheduling_processing">Still working…</string>
    <string name="scheduling_decline_dialog">Are you sure you want to decline this block\?</string>
    <string name="scheduling_decline_dialog_positive">Decline</string>
    <string name="scheduling_decline_dialog_negative">Cancel</string>
    <string name="scheduling_offer_price_hide_tips_amount">%1$s + tips</string>
    <string name="scheduling_offer_delivery_cutoff_time">Deliveries up to: %1$s</string>
    <string name="scheduling_offer_delivery_cutoff">Deliveries up to</string>
    <string name="scheduling_tips_breakdown_disclaimer">Amazon contribution for this block is %1$s and you will receive 100%% of customer tips. While no tip is guaranteed, more than 25%% of similar blocks in your area recently earned at least %2$s in tips.</string>
    <string name="hour_abbreviated_lc">%1$d hr</string>
    <string name="hour_abbreviated">%1$d Hr</string>
    <string name="minutes_abbreviated_lc">%1$d min</string>
    <string name="minutes_abbreviated">%1$d Min</string>
    <string name="minutes_full_text">%1$d Minutes</string>
    <string name="scheduling_offer_filter">Filter</string>
    <string name="scheduling_offer_show_excluded_blocks">Show all other offers</string>
    <string name="scheduling_offer_hide_excluded_blocks">Hide all other offers</string>
    <string name="scheduling_offer_filter_show_offers">Show offers</string>
    <string name="scheduling_offers_filter_by_title">Filter Offers by</string>
    <string name="scheduling_offers_filter_by_time">View offers between these times</string>
    <string name="scheduling_offers_filter_by_time_v2">When would you like to work\?</string>
    <string name="scheduling_offers_filter_by_orders">How much would you like to work\?&lt;![CDATA[&lt;b&gt;&lt;font color=\"black\"&gt;*&lt;/font&gt;&lt;/b&gt;]]&gt;</string>
    <string name="scheduling_offers_delivery_request_learn_more_text">Have questions\? Tap &lt;![CDATA[&lt;span&gt;here&lt;/span&gt;]]&gt; to learn more</string>
    <string name="scheduling_offers_filter_by_orders_default_text">Any number of Orders and Delivery Requests</string>
    <string name="scheduling_offers_filter_orders_text">Orders (%1$s)</string>
    <string name="scheduling_offers_filter_orders_text_any">Any</string>
    <string name="scheduling_offers_filter_station_text">Stations (%1$d)</string>
    <string name="scheduling_offer_no_filtered_offers">There are no offers available that match your filters. Either check back soon or expand your filters to see other offers.</string>
    <string name="scheduling_offer_rewards_level_label">Level %1$d Rewards</string>
    <string name="scheduling_offer_early_access_label">Early Access</string>
    <string name="scheduling_offer_fsm_label">Grocery shopping and delivering</string>
    <string name="remove_orders_filter">remove orders filter</string>
    <string name="delivery_request_title">Delivery Request</string>
    <string name="scheduling_offer_expenses_breakdown_with_tips">%1$s/hr + %2$s expenses + tips</string>
    <string name="scheduling_offer_expenses_breakdown_no_tips">%1$s/hr + %2$s expenses</string>
    <string name="scheduling_offer_with_expenses_includes_tips">+ tips</string>
    <string name="scheduling_offer_estimated_price">%1$s (estimate)</string>
    <string name="scheduling_offer_expenses_breakdown_disclaimer_no_tips">You will receive payment based on actual time and distance of your route. We estimate you will have %1$s in cellphone/mileage expenses.</string>
    <string name="scheduling_offer_expenses_breakdown_disclaimer_with_tips">You will receive payment based on actual time and distance of your route. You will receive 100%% of customer tips. While no tip is guaranteed, more than 25%% of similar blocks in your area recently earned at least %1$s in tips. We estimate you will have %2$s in cellphone/mileage expenses.</string>
    <string name="scheduling_offer_accept_success">You accepted the block.</string>
    <string name="scheduling_offer_reject_success">You declined the block.</string>
    <string name="scheduling_offer_request_success">You requested the block.</string>
    <string name="scheduling_offer_withdraw_success">You have withdrawn the block</string>
    <string name="Return_package_alert_title">Return package to station</string>
    <string name="Return_package_reason_info">This delivery has been canceled. Please keep the package with you and continue to the other stops on your route.</string>
    <string name="Scheduling_offer_canceled">Sorry, this block has been canceled.</string>
    <string name="Scheduling_offer_taken">Sorry, this block has been taken.</string>
    <string name="Scheduling_offer_scheduling_limit">Sorry, this block is no longer available.</string>
    <string name="Scheduling_offer_too_many_requests">You’ve tapped too many times or for too long. Please try again later.</string>
    <string name="scheduling_empty_offer_list_state">No offers available. Check back soon.</string>
    <string name="schedule_deliveries_now">Check for available blocks</string>
    <string name="home_delivering_schedule_more">Earn extra $ by scheduling more deliveries.</string>
    <string name="notification_scheduling_non_exclusive_offer_alert">Accept them before they\\\'re gone.</string>
    <string name="notification_scheduling_non_exclusive_alert_title">Delivery blocks available</string>
    <string name="notification_scheduling_exclusive_alert">Accept it before it expires</string>
    <string name="notification_scheduling_exclusive_alert_title">New reserved block</string>
    <string name="notification_scheduling_surge_alert">Accept these blocks before they\\\'re gone.</string>
    <string name="notification_scheduling_surge_alert_title">Increased rates available</string>
    <string name="notification_workschedule_validation_failure">Hour ranges must be at least 30 minutes long and cannot overlap.</string>
    <string name="transporter_delivery_service_greet_customer">Greet the customer and follow the steps for the service(s).</string>
    <string name="transporter_delivery_service_ensure_completion">Please ensure all steps are complete before continuing your route.</string>
    <string name="transporter_delivery_service_unsafe_contact">If you feel unsafe, contact Support and ask them to cancel the service.</string>
    <string name="transporter_delivery_stop_include_service">This stop includes delivery services</string>
    <string name="schedulingoffers_availability_display_text">Mark your available times on the calendar so we can send you offers that work for you.\n\nOnce you accept an offer, it will show up on your calendar.\n\nAdditional offers may be available. Check offers in the main menu as often as you wish.</string>
    <string name="autobot_enabled_header">Looking for Offers</string>
    <string name="offers_experience_reserve_now_header">Reserve Now</string>
    <string name="offers_experience_request_header">Request</string>
    <string name="scheduling_autobot_no_offers_header">No offers</string>
    <string name="scheduling_autobot_no_offers_body">We\\\'ll continue checking for offers. For reserved offers, keep your availability settings up-to-date. To get one minute reserved offers, please stay on this screen.</string>
    <string name="scheduling_autobot_no_offers_availability_link">Update availability</string>
    <string name="app_feedback_selection_activity_title">LEAVE FEEDBACK</string>
    <string name="app_feedback_edit_text_activity_title">TELL US MORE</string>
    <string name="app_feedback_done_activity_title">THANK YOU</string>
    <string name="app_feedback_selection_sub_title">Choose a category</string>
    <string name="app_feedback_edit_text_sub_title">Category: %1$s</string>
    <string name="app_feedback_edit_done_sub_title">We appreciate your feedback</string>
    <string name="app_feedback_content_edit_text_hint">If you are currently making deliveries and need immediate help, please call support directly. We regularly review feedback to identify problems and improve your experience. We look forward to hearing from you! We hope you\\\'ll tell us about:\n\nThings that frustrate you\nHow we can improve your experience\nWhat you like about delivering for Amazon</string>
    <string name="app_feedback_category_signing_up">Signing up</string>
    <string name="app_feedback_category_scheduling_deliveries">Scheduling deliveries</string>
    <string name="app_feedback_category_pickup">Pickup</string>
    <string name="app_feedback_category_delivery">Delivery</string>
    <string name="app_feedback_category_navigation_and_maps">Maps, routes, and addresses</string>
    <string name="app_feedback_category_getting_paid">Getting paid</string>
    <string name="app_feedback_category_device_and_connectivity">Device and connectivity</string>
    <string name="app_feedback_category_other">Other</string>
    <string name="app_feedback_submit_text">Submit Feedback</string>
    <string name="app_feedback_done_text">Done</string>
    <string name="training_activity_title">VIDEOS</string>
    <string name="healthcare_subsidy_title">Healthcare Subsidy</string>
    <string name="widget_next_shift_tap_to_schedule">Tap to schedule a delivery</string>
    <string name="widget_next_shift_time">%1$s - %2$s</string>
    <string name="scanless_tap_continue">Tap continue</string>
    <string name="delivery_scanless_stop_arrival_primary_button">Review order</string>
    <string name="delivery_scanless_stop_review_primary_button">Continue</string>
    <string name="pickup_scanless_stop_review_primary_button">Continue</string>
    <string name="nonscannable_package_view_bag_number_text">Order from</string>
    <string name="nonscannable_item_view_bag_number_text">Inside order from</string>
    <string name="nonscannable_item_quantity_text">%1$d x %2$s</string>
    <string name="stop_details_multiple_exceptions_title">Multiple exceptions</string>
    <string name="stop_details_delivery_exception_title">Delivery exceptions</string>
    <string name="stop_details_delivered_title">Delivered</string>
    <string name="stop_details_not_delivered_title">Attempted</string>
    <string name="stop_details_picked_up_title">Picked up</string>
    <string name="stop_details_not_picked_up_title">Not picked up</string>
    <string name="stop_details_unable_to_deliver_title">Unable to deliver</string>
    <string name="stop_details_arrived_title">Arrived</string>
    <string name="stop_details_delivery_item_exception">%1$d item(s) %2$s</string>
    <string name="stop_details_delivery_package_exception">%1$d %2$s</string>
    <string name="stop_details_view_returned_button_label">View %1$d returned item(s)</string>
    <string name="stop_details_unknown_scannable">Unknown</string>
    <string name="stop_details_dispatcher_instruction_title">Dispatcher instruction</string>
    <string name="stop_details_delivery_recipient_with_reason_format">%1$s, %2$s</string>
    <string name="stop_details_unable_to_pickup_reason_title">Unable to pickup because</string>
    <string name="stop_details_do_not_deliver_title">Do not deliver</string>
    <string name="stop_details_canceled_body">The customer has either canceled or rescheduled their delivery.</string>
    <string name="cash_on_delivery_title_collect_payment">Collect payment upon delivery</string>
    <string name="cash_on_delivery_title_collect_payment_and_exchange">Collect payment and item</string>
    <string name="cash_on_delivery_title_payment_collected">Payment collected</string>
    <string name="cash_on_delivery_exchange_title_payment_collected">Payment and item collected</string>
    <string name="cash_on_delivery_title_paid_in_cash">%1$s paid in cash</string>
    <string name="cash_on_delivery_title_paid_with_card">%1$s paid with card</string>
    <string name="cash_on_delivery_title_paid_with_card_or_paylink">%1$s paid with card or paylink</string>
    <string name="cash_on_delivery_title_paid_with_paylink">%1$s paid with paylink</string>
    <string name="cash_on_delivery_title_paid_with_paylink_signature">I have paid a total of %1$s via PayLink</string>
    <string name="cash_on_delivery_cash_change_received">%1$s returned in cash</string>
    <string name="cash_on_delivery_sva_change_received">%1$s credited to the Amazon Pay balance of %2$s (Mobile phone: %3$s)</string>
    <string name="cash_on_delivery_sva_change_received_new_flow">%1$s credited to the Amazon Pay balance of %2$s</string>
    <string name="cash_on_delivery_signature_exact">I have paid a total of %1$s in cash.</string>
    <string name="cash_on_delivery_signature_cash_change">I confirm that I have paid a total of &lt;b&gt;%1$s&lt;/b&gt; in cash. From this amount, &lt;b&gt;%4$s&lt;/b&gt; is payment towards the Amazon.in order, and &lt;b&gt;%2$s&lt;/b&gt; has been returned in cash.</string>
    <string name="cash_on_delivery_signature_load_change">I confirm that I have paid a total of &lt;b&gt;%1$s&lt;/b&gt; in cash. From this amount, &lt;b&gt;%4$s&lt;/b&gt; is payment towards the Amazon.in order, and &lt;b&gt;%3$s&lt;/b&gt; will be credited to the Amazon Pay balance: Money (issued by APIPL) of %5$s (mobile phone: %6$s). Amazon Pay customer care contact details: 1800-3000-1571</string>
    <string name="cash_on_delivery_signature_load_change_new_flow">I confirm that I have paid a total of &lt;b&gt;%1$s&lt;/b&gt; in cash. From this amount, &lt;b&gt;%4$s&lt;/b&gt; is payment towards the Amazon.in order, and &lt;b&gt;%3$s&lt;/b&gt; will be credited to the Amazon Pay balance: Money (issued by APIPL) of %5$s. Amazon Pay customer care contact details: 1800-3000-1571</string>
    <string name="cash_on_delivery_signature_mixed_change">I confirm that I have paid a total of &lt;b&gt;%1$s&lt;/b&gt; in cash. From this amount, &lt;b&gt;%4$s&lt;/b&gt; is payment towards the Amazon.in order, &lt;b&gt;%3$s&lt;/b&gt; will be credited to the Amazon Pay balance: Money (issued by APIPL) of %5$s (mobile phone: %6$s), and &lt;b&gt;%2$s&lt;/b&gt; has been returned in cash. Amazon Pay customer care contact details: 1800-3000-1571</string>
    <string name="cash_on_delivery_signature_mixed_change_new_flow">I confirm that I have paid a total of &lt;b&gt;%1$s&lt;/b&gt; in cash. From this amount, &lt;b&gt;%4$s&lt;/b&gt; is payment towards the Amazon.in order, &lt;b&gt;%3$s&lt;/b&gt; will be credited to the Amazon Pay balance: Money (issued by APIPL) of %5$s, and &lt;b&gt;%2$s&lt;/b&gt; has been returned in cash. Amazon Pay customer care contact details: 1800-3000-1571</string>
    <string name="cash_on_delivery_transaction_id">Transaction ID: %1$s</string>
    <string name="cash_on_delivery_signature_transaction_id">%2$s [Transaction ID: %1$s]</string>
    <string name="cash_on_delivery_details_text">Customer owes %1$s</string>
    <string name="cash_on_delivery_payment_amount_paid">Amount paid</string>
    <string name="cash_on_delivery_confirm_amount">Confirm</string>
    <string name="cash_on_delivery_continue_button">Continue</string>
    <string name="cash_on_delivery_confirm_amount_change">%1$s cash change</string>
    <string name="cash_on_delivery_confirm_load_account">%1$s to Amazon Pay</string>
    <string name="cash_on_delivery_change_due">Change due to customer: %1$s</string>
    <string name="cash_on_delivery_select_return_note">Choose an option to proceed:</string>
    <string name="cash_on_delivery_amazon_pay_max">*Max cash load allowed on this transaction is %1$s</string>
    <string name="cash_on_delivery_amazon_pay_max_dialog">*%1$s will be loaded into Amazon Pay balance</string>
    <string name="cash_on_delivery_confirm_payment">Confirm payment</string>
    <string name="cash_on_delivery_confirmation_subheader_cash">Collect the amount below in cash</string>
    <string name="cash_on_delivery_confirmation_subheader_card">Did you charge the customer the amount below\?</string>
    <string name="cod_external_card_payment_subheader">Open your card reader app and process the payment</string>
    <string name="cod_external_card_payment_open_app">Open app</string>
    <string name="cod_cash_received_confirmation">Confirm</string>
    <string name="cod_reenter_cash_received_confirmation">Re enter \\\'Cash Received\\\' amount</string>
    <string name="cod_cash_received_confirm_not_equal">Amount mismatch. Please re-enter</string>
    <string name="cash_load_modal_header">Check with the customer if they want to load cash to Amazon Pay Balance.</string>
    <string name="cash_load_modal_body">Your delivery is completed, but if the customer asks you to load cash into Amazon Pay balance, then select \"Yes\" below</string>
    <string name="cash_load_modal_positive_button">Yes, load cash</string>
    <string name="cash_load_modal_negative_button">No, go to the next stop</string>
    <string name="decimal_key_error">Decimal values not allowed</string>
    <string name="io_home_title_activity">Amazon Flex</string>
    <string name="io_available_now_modal_body">Be ready to accept offers near your location that start immediately.\nThese offers are available in addition to your regular blocks.</string>
    <string name="io_available_now_modal_body1">Be ready to accept offers near your location that start </string>
    <string name="io_available_now_modal_body2">immediately</string>
    <string name="io_available_now_modal_body3">.\nThese offers are available in addition to your regular blocks.</string>
    <string name="io_available_now_modal_title">Available Now</string>
    <string name="io_learn_more">Learn more</string>
    <string name="io_nearby_offers">Nearby offers that start immediately</string>
    <string name="io_check_for_offers">Upcoming offers</string>
    <string name="io_how_to_get_offers">How to get offers</string>
    <string name="io_tooltip_body">Be near pick up locations during busy times to improve your chance of getting an offer.</string>
    <string name="io_tooltip_footer">More details</string>
    <string name="io_instruction_title">You don\\\'t have upcoming work scheduled for today.</string>
    <string name="io_instruction_body">&lt;![CDATA[Visit &lt;b&gt;Offers&lt;/b&gt; to view and accept offers or mark yourself as &lt;b&gt;Available Now&lt;/b&gt; above.]]&gt;</string>
    <string name="io_offer_expired_message">This offer has expired.</string>
    <string name="io_offer_get_failure_message">There\\\'s a problem showing your offer.</string>
    <string name="io_offer_accept_failure_message">Accepting your offer didn\\\'t work.</string>
    <string name="io_offer_reject_failure_message">Rejecting your offer didn\\\'t work.</string>
    <string name="io_offer_generic_error_message">There\\\'s a problem with your offer.</string>
    <string name="io_no_network_title">Network connection not found</string>
    <string name="io_no_network_text">Please connect to a network and try again.</string>
    <string name="io_delivering_title">Finish your remaining deliveries.</string>
    <string name="io_delivering_text">Earn more by accepting additional offers.</string>
    <string name="io_delivering_button_text">Continue delivering</string>
    <string name="io_distance_text">%1$s %2$s</string>
    <string name="io_distance_estimated_text">%1$s %2$s (estimated)</string>
    <string name="io_time_range_text">%1$d-%2$d minutes (estimated)</string>
    <string name="set_real_time_status_error">Sorry, we were unable to set your Available Now status. Try again later.</string>
    <string name="stop_details_retry_delivery_button_text">Retry Delivery</string>
    <string name="stop_details_pickup_bottles_button_text">Start Bottle Pickup</string>
    <string name="stop_details_retry_pickup_button_text">Retry Pickup</string>
    <string name="stop_details_retry_exchange_button_text">Retry Exchange</string>
    <string name="select_service_area_not_listed_button">My Area isn\\\'t listed</string>
    <string name="select_service_area_subheader_text">Where are you delivering today\?</string>
    <string name="select_region_subheader_text">Select your region</string>
    <string name="da_on_duty_no_regions_available">No regions available to display</string>
    <string name="da_on_duty_no_service_areas_available">No Service Areas available to display</string>
    <string name="quick_barcode_entry_hint_text">Example: 1A2B</string>
    <string name="quick_barcode_entry_duplicate_scan_message">Package ID already scanned</string>
    <string name="quick_barcode_entry_wrong_barcode_message">Wrong package ID</string>
    <string name="quick_barcode_entry_selection_dialog_cancel_button_text">Cancel</string>
    <string name="quick_barcode_entry_title_text">Please enter the &lt;b&gt;first&lt;/b&gt; 4 digits of the package ID:</string>
    <string name="quick_barcode_entry_selection_dialog_header_text">Multiple Package IDs matched your entry</string>
    <string name="quick_barcode_entry_selection_dialog_body_text">Please select one</string>
    <string name="miles">mi</string>
    <string name="kms">kms</string>
    <string name="next_delivery_distance_text">The next delivery is about %1$s %2$s away</string>
    <string name="add_deliveries_button_text">Add deliveries to this stop</string>
    <string name="select_all_button_text">Select all</string>
    <string name="unselect_all_button_text">Deselect all</string>
    <string name="group_locker_delivery_confirmation_subheader">Select one package\n Match ID and scan at locker\nScan with App\nPlace in locker</string>
    <string name="group_delivery_confirmation_subheader">Select one or more customers to continue</string>
    <string name="group_delivery_add_or_remove_delivery">Add or remove deliveries</string>
    <string name="offline_maps_title_activity">OFFLINE MAPS</string>
    <string name="offline_maps_download">Download</string>
    <string name="offline_maps_view_downloaded">View Downloaded Regions</string>
    <string name="title_offline_maps">Offline Maps</string>
    <string name="offline_maps_download_button_text">Download</string>
    <string name="offline_maps_download_subheader_text">Select a region to download</string>
    <string name="offline_maps_no_regions_available">No regions available to download</string>
    <string name="offline_maps_view_downloaded_regions_subheader_text">Downloaded regions</string>
    <string name="delete_maps">Delete map</string>
    <string name="cause_crash">Crash it!</string>
    <string name="crash_text">TEST CRASH</string>
    <string name="logging_choose_log_level">Please choose a log level</string>
    <string name="logging_create">Create Logs</string>
    <string name="logging_debug">Debug</string>
    <string name="logging_disabled">Logging Disabled</string>
    <string name="logging_enabled">Logging Enabled</string>
    <string name="logging_error">Error</string>
    <string name="logging_info">Info</string>
    <string name="logging_label">Logging Test</string>
    <string name="logging_toggle">Toggle Logging</string>
    <string name="logging_upload">Upload Logs</string>
    <string name="logging_uploaded">Logs Uploaded</string>
    <string name="logging_verbose">Verbose</string>
    <string name="logging_warning">Warning</string>
    <string name="sdk_test_panel_menu_title">SDK Test Panel</string>
    <string name="weblab_title_activity">Treatment overrides</string>
    <string name="weblab_add_override">Add override</string>
    <string name="weblab_add_bulk_override">Add Bulk Override</string>
    <string name="weblab_reset_override">Reset All Overrides</string>
    <string name="weblab_restart_app">Restart app</string>
    <string name="weblab_quick_select_dialog_title">Select a treatment override</string>
    <string name="weblab_quick_select_dialog_enter_manually">Set override</string>
    <string name="weblab_treatment_override_hint">Treatment override (e.g., T1)</string>
    <string name="srecon_title_activity">Feature Overrides</string>
    <string name="srecon_selection_message">Select your override</string>
    <string name="srecon_selection_double_message">Select your Double override</string>
    <string name="srecon_selection_string_message">Select your String override</string>
    <string name="srecon_enable">Enable</string>
    <string name="srecon_disable">Disable</string>
    <string name="launch_document_activity">Launch Document</string>
    <string name="image_error_message">Photo capture failed.</string>
    <string name="unattended_deliveries_title">Step back and capture the package in the white frame.</string>
    <string name="take_photo_attended_subtitle">Make sure not to capture faces in the photo.</string>
    <string name="title_activity_survey">Your Voice Matters</string>
    <string name="survey_welcome_page_button_skip_survey">Skip Feedback</string>
    <string name="survey_welcome_page_button_start_survey">Start Feedback</string>
    <string name="survey_page_counter">%1$d of %2$d</string>
    <string name="survey_page_button_skip_question">Skip this Question</string>
    <string name="next_button">Next</string>
    <string name="submit_button">Submit</string>
    <string name="survey_item_free_form_entry_hint">Enter your response.</string>
    <string name="survey_completed_notification">Thank you! We\\\'ll use your feedback to improve your experience.</string>
    <string name="survey_intro_screen_header_text">App feedback</string>
    <string name="survey_intro_screen_body_text">We are testing out new features and bug fixes, and we would love for your feedback based on your experience delivering today.</string>
    <string name="survey_feedback_content_edit_text_hint">Please describe the app issues you experienced today. Provide as many details as possible, including error messages or support codes, so that we can better diagnose and fix the problem.</string>
    <string name="survey_feedback_in_app_feedback_question">You\\\'re using a new version of the Amazon Flex app only available to a few drivers. Did you notice any app issues today\?</string>
    <string name="survey_feedback_in_app_feedback_maps_question">We have updated the Maps experience in the app.\n\n1. Please ensure you submit feedback through the app menu: &lt;b&gt;App menu &amp;gt; Feedback &amp;gt; Maps, routes, and addresses&lt;/b&gt;\n\n2. Did you face any issues while navigating\?</string>
    <string name="stop_detail_display_label">Package ID %1$s</string>
    <string name="stop_detail_container_summary">Container %1$s</string>
    <string name="stop_detail_not_in_container">Not in container</string>
    <string name="detail_drawer_close_drawer">Close Drawer</string>
    <string name="navigation_disclaimer_dialog_title">Caution using navigation</string>
    <string name="navigation_disclaimer_dialog_message">Turn-by-turn navigation is not optimized for commercial motor vehicles (CMVs). Some routes may not be safe or legal for CMVs to use. Always follow road signs and legal requirements.</string>
    <string name="dock_door_checkin_toolbar_title">Dock door entry</string>
    <string name="dock_door_scan_text_below_toolbar">Scan code on dock door</string>
    <string name="dock_door_scan_start_instructions_CN51">Use the built-in scanner on your device to scan the dock door.</string>
    <string name="call_your_carrier_dialog_title">Call Your Carrier</string>
    <string name="call_your_carrier_dialog_message">Your carrier will provide instructions about this delivery</string>
    <string name="navigation_fallback_dialog_title">Unable to navigate to this destination</string>
    <string name="navigation_fallback_dialog_message">Use another maps application to get to this location</string>
    <string name="maps_external_spotlight_description">Tap and select CoPilot GPS for safer navigation based on your vehicle type</string>
    <string name="acknowledgement_dialog_button">Acknowledge</string>
    <string name="transport_request_detail_exchange_attempted_at">Exchange attempted at %s</string>
    <string name="itinerary_exchange_successful_at">Exchanged at %1$s</string>
    <string name="stop_details_exchange_attempted_title">Exchange Attempted</string>
    <string name="stop_details_exchange_successful_title">Exchanged</string>
    <string name="stop_details_unable_to_exchange_reason_title">Unable to exchange because</string>
    <string name="exchange_declaration_text_on_signature_fragment">\"I %1$s hereby declare that I am not required to comply with any of the tax regulations/ provisions as I am not involved in any resale activity in the course of or in furtherance of a business. I also certify that the items being sold are old &amp;amp; used products. I further understand and agree to the terms and conditions of the program available on www.amazon.in\"</string>
    <string name="successful_exchange_title_on_summary_page">Item collected</string>
    <string name="packages_already_scanned_string_banner">Packages that have already been scanned</string>
    <string name="virtual_id_itinerary">TODAY\\\'S ITINERARY</string>
    <string name="virtual_id_on_duty">ON DUTY</string>
    <string name="virtual_id_date_time">%1$s, %2$s</string>
    <string name="virtual_id_photo_unavailable">Your photo is not available at this time.</string>
    <string name="virtual_id_off_duty_title">Try again later</string>
    <string name="virtual_id_off_duty_message">You can view your photo ID when you check in to work.</string>
    <string name="virtual_id_off_duty_button">OK</string>
    <string name="photo_upload_entry_title">Photo ID</string>
    <string name="photo_upload_entry_prompt">Please submit your photo to continue to be eligible to deliver with Amazon Flex.</string>
    <string name="photo_upload_entry_detail">&lt;b&gt;Tips:&lt;/b&gt;\n • Remove sunglasses, hats, face masks or other items that cover your face\n • Face the camera directly\n • Use a neutral background, such as a blank wall\n • Make sure the photo is well-lit and clear\n • Don’t take a photo of a photo</string>
    <string name="photo_upload_entry_instructions">This photo will be used for your virtual ID and for identification purposes.</string>
    <string name="photo_upload_entry_continue">Take Photo</string>
    <string name="photo_upload_error_title">Photo Upload Failed</string>
    <string name="photo_upload_error_body">There\\\'s a problem uploading your photo. You can continue without uploading, or retry the upload.</string>
    <string name="photo_upload_error_retry">Retry Upload</string>
    <string name="photo_upload_error_dont_retry">Don\\\'t Retry</string>
    <string name="photo_upload_network_connection_title">No network connection</string>
    <string name="photo_upload_network_connection_body">You\\\'re not connected to the internet. Check your device settings and try again.</string>
    <string name="photo_upload_device_settings">Go to device settings</string>
    <string name="photo_upload_try_again">Try again</string>
    <string name="photo_upload_failed">Photo upload failed, please retry</string>
    <string name="photo_upload_success_title">Thank you!</string>
    <string name="photo_upload_success_message">Your photo was uploaded.</string>
    <string name="photo_upload_sign_out_header">Sign Out</string>
    <string name="photo_upload_sign_out_message">Your photo is still uploading. If you sign out now, the photo will be lost and you\\\'ll need to take another one.</string>
    <string name="photo_upload_sign_out_primary_button">Sign Out</string>
    <string name="photo_upload_sign_out_secondary_button">Cancel</string>
    <string name="photo_upload_capture_title">Photo</string>
    <string name="photo_upload_capture_preview_subheader">Place your head and shoulders in the lines.</string>
    <string name="photo_upload_capture_review_subheader">When you\\\'re ready to submit your final picture, tap Use photo.</string>
    <string name="photo_upload_capture_review_retake">Retake</string>
    <string name="photo_upload_capture_review_use_photo">Use photo</string>
    <string name="photo_upload_capture_warning_title">Use this photo\?</string>
    <string name="photo_upload_capture_warning_body">You won\\\'t be able to change it later. Make sure your head and shoulders are visible and aren\\\'t hidden by hats, sunglasses or other items that change your appearance.</string>
    <string name="call_customer">Call Customer</string>
    <string name="call_customer_text_details">Please call the customer before selecting this reason</string>
    <string name="call_popup_close_description">Dismiss</string>
    <string name="call_popup_header_text">Having trouble with your delivery\?</string>
    <string name="call_popup_body_one">Calling the customer can help.</string>
    <string name="call_popup_body_one_with_texting">Texting or calling the customer can help.</string>
    <string name="call_popup_body_two">They can provide information to help you access or locate a safe delivery location.</string>
    <string name="call_popup_button_text">Call customer</string>
    <string name="call_popup_text_button_text">Text customer</string>
    <string name="title_activity_phone_number_verification">Verify Phone Number</string>
    <string name="verification_code_sent_instruction">A code was texted to this number:\n</string>
    <string name="verification_request_code_instruction">Tap REQUEST CODE to receive a code to verify your phone number.</string>
    <string name="verification_request_code">Request Code</string>
    <string name="verification_request_new_code">Request new code</string>
    <string name="verification_enter_valid_number">Enter valid number</string>
    <string name="verification_phone_number">Phone Number</string>
    <string name="verification_country_code">Country Code</string>
    <string name="verification_enter_code">Enter code</string>
    <string name="verification_enter_country_code">Enter country code</string>
    <string name="verification_code">Code</string>
    <string name="verification_code_submit">Submit</string>
    <string name="phone_number_input_instruction">Add or update the mobile phone number you\\\'ll use to make deliveries.</string>
    <string name="stop_phone_verification_dialog_title">Stop Phone Verification\?</string>
    <string name="stop_phone_verification_dialog_message">We\\\'ll need to verify your phone number before you can call customers or Support while delivering.</string>
    <string name="stop_phone_verification_mandatory_dialog_message">We\\\'ll need to verify your phone number before you can start delivering packages.</string>
    <string name="stop_phone_verification_dialog_action_verify_now">Verify</string>
    <string name="stop_phone_verification_dialog_action_verify_later">Stop</string>
    <string name="incorrect_verification_code_dialog_title">Incorrect Code</string>
    <string name="incorrect_verification_code_dialog_message">There\\\'s a problem with that code, please enter it again.</string>
    <string name="unable_to_verify_dialog_title">Can\\\'t verify phone number</string>
    <string name="unable_to_verify_dialog_message">We\\\'re unable to verify your phone number. Please try again in a few minutes.</string>
    <string name="update_phone_number">update phone number</string>
    <string name="unable_to_contact_number">We\\\'re unable to contact you using the number you provided.</string>
    <string name="please_update_number">Please update your phone number to continue.</string>
    <string name="driver_support_agent">Support Agent</string>
    <string name="survey_menu">Survey</string>
    <string name="scan_arrival_code">Scan Arrival Code</string>
    <string name="arrival_scan_title">\@string/scan_arrival_code</string>
    <string name="arrival_scan_subheader">Align code in the frame below</string>
    <string name="arrival_scan_success">Ready to Start Pickup!</string>
    <string name="arrival_scan_failure">Invalid barcode scanned</string>
    <string name="arrival_scan_instructions_continue">Continue</string>
    <string name="arrival_scan_instructions_header">\@string/scan_arrival_code</string>
    <string name="arrival_scan_update_status">Loading…</string>
    <string name="arrival_scan_override_network_fails">Error validating override. Please ask the associate to resend an override</string>
    <string name="arrival_scan_network_fails">Something went wrong. Please scan again</string>
    <string name="arrival_scan_instructions_content">Before you can scan packages, you must meet an Amazon representative to scan an Arrival code.</string>
    <string name="arrival_code_station_pickup_instructions_content"> • Meet an Amazon representative to scan the Arrival QR code\n• Scan each package or bag, load in vehicle before departing</string>
    <string name="arrival_code_gsf_pickup_instructions_content"> • Proceed into the station to scan the Arrival QR Code at the kiosk\n• Scan each package or bag, load in vehicle before departing</string>
    <string name="arrival_scan_gsf_lateness_popup_title">You are late to scan Arrival QR code</string>
    <string name="arrival_scan_gsf_lateness_popup_body">Please call support for assistance</string>
    <string name="arrival_scan_gsf_deadline_subheader">Please scan the QR Code by %1$s</string>
    <string name="item_verification_verify_button_text">Verify</string>
    <string name="item_verification_activity_title">Verify</string>
    <string name="item_verification_stop_completed">Stop Completed</string>
    <string name="item_verification_stop_completed_without_verification">Verification Failed</string>
    <string name="item_verification_yes_button_text">Yes</string>
    <string name="item_verification_no_button_text">No</string>
    <string name="item_verification_next_button_text">Next</string>
    <string name="item_verification_wrong_item_button_text">Wrong Item</string>
    <string name="item_verification_steps">Step %1$d of %2$d</string>
    <string name="item_verification_incorrect_input_message">You have entered incorrect information.</string>
    <string name="item_verification_failed_blocking_screen_large_text">The item destination state doesn\\\'t allow the delivery of items wrapped in plastic packaging material.\nLeave this package behind with the seller.</string>
    <string name="item_verification_failed_blocking_screen_title">Do Not Pickup This Package</string>
    <string name="item_verification_edit_text_hint">-----</string>
    <string name="item_verification_open_box_delivery_title">Open Box Delivery</string>
    <string name="item_verification_continue">Continue</string>
    <string name="item_verification_preverification_success_banner">We have checked this already, do not perform this check</string>
    <string name="multiple_access_code_title_collapsed">Access code</string>
    <string name="multiple_access_code_title_expanded">Access codes</string>
    <string name="multiple_access_code_expand_link">Show more</string>
    <string name="multiple_access_code_collapse_link">Show less</string>
    <string name="io_detail_toolbar_title">new offer</string>
    <string name="io_detail_immediately_label_strong">immediately</string>
    <string name="io_tr_show_details_toggle">Show details</string>
    <string name="io_decline_button">Decline</string>
    <string name="io_detail_includes_tips">Includes Tips</string>
    <string name="io_detail_price_tips_breakdown_disclaimer">%1$s + estimated tips</string>
    <string name="io_detail_surge">SURGE</string>
    <string name="io_detail_delivery_info">Delivery Estimate</string>
    <string name="io_detail_tr_info_deliver">Deliver</string>
    <string name="io_detail_tr_info_pickup">Pickup</string>
    <string name="io_detail_tr_info_start">Start</string>
    <string name="io_tr_hide_details_toggle">Hide details</string>
    <string name="io_pickup_estimate_zero_minutes">Pick up and Deliver immediately.</string>
    <string name="io_fsm_detail_description">Shop and Deliver customer orders</string>
    <string name="io_fsm_shop_and_deliver">Deliver orders after shopping</string>
    <string name="Instant_offer_canceled">Sorry, this real-time offer has been canceled.</string>
    <string name="Instant_offer_taken">Sorry, this real-time offer has been taken.</string>
    <string name="Instant_offer_scheduling_limit">Sorry, this real-time offer is no longer available.</string>
    <string name="Instant_offer_uncertain_result">Due to connectivity error we need to retry in order verify if the instant offer was accepted successfully.</string>
    <string name="io_estimated_time_distance_format">%1$s, %2$s</string>
    <string name="io_tr_address_format">%1$s, %2$s</string>
    <string name="swipe_to_accept_text">Swipe to accept</string>
    <string name="io_stops_number_text">%1$s stops</string>
    <string name="io_itinerary_info_stop_header">Stops</string>
    <string name="io_itinerary_info_stop_disclaimer">*While we make effort to obtain business names, these names may be inaccurate or incomplete.</string>
    <string name="io_itinerary_info_stop_distance">%1$s away</string>
    <string name="io_itinerary_info_stop_details_available_later">Details available later</string>
    <string name="io_itinerary_info_stops_available_later">Additional stops available later</string>
    <string name="maps_toggle_to_10_title">ATTENTION: Switch Maps\?</string>
    <string name="maps_toggle_to_10_message">You should only switch to the older version of Maps if you require Offline Navigation. Selecting Yes will restart the app. Continue\?</string>
    <string name="maps_toggle_to_20_title">Switch Maps\?</string>
    <string name="maps_toggle_to_20_message">You will now be switched to the new version of Maps. This will restart the app. Continue\?</string>
    <string name="instantoffers_acceptance_connectivity_error_title">CONNECTIVITY LOST</string>
    <string name="instantoffers_acceptance_error_message">Oops, we lost internet connectivity while accepting the offer, and it may be assigned to you now.\n\nPlease reconnect to the internet and try again.\n\nIf you can\\\'t reconnect, call Driver Support immediately to determine whether the order is assigned to you. Failure to pick up and deliver assigned orders can affect your participation with Amazon Flex if it happens on multiple occassions.</string>
    <string name="instantoffers_acceptance_error_not_assigned_title">OFFER STATUS</string>
    <string name="instantoffers_acceptance_error_not_assigned_message">Sorry, we weren\\\'t able to assign this offer to you. Don\\\'t worry though, it won\\\'t count against you.</string>
    <string name="instantoffers_acceptance_error_not_assigned_button">Close</string>
    <string name="instantoffers_call_support_dialog_tittle">Need more help\?</string>
    <string name="instantoffers_call_support_dialog_body">Please try tapping the Try Again button before you call support.</string>
    <string name="instantoffers_call_support_dialog_cancel">Cancel</string>
    <string name="instantoffers_call_support_dialog_confirm">Call</string>
    <string name="save_card_declaration">I consent to save this card to my Amazon account.</string>
    <string name="save_card_information">Your card will be securely saved for faster checkout on Amazon. Amazon does not store CVV.</string>
    <string name="ovd_id_pin_description">To proceed with cash-load, KYC confirmation code is needed. Customer will get the code on SMS/email after submitting KYC details online at &lt;b&gt;https://amazon.in/OVD&lt;/b&gt;</string>
    <string name="ovd_id_pin_input_title">KYC confirmation code</string>
    <string name="ovd_id_wrong_pin">This PIN code isn\\\'t correct. Please try again.</string>
    <string name="ovd_id_footer_message">To return the change in cash, press back button and select \\\'Return Change\\\'</string>
    <string name="ovd_id_pin_failure_error_text">KYC confirmation code is incorrect. Please try again.</string>
    <string name="ovd_id_pin_fragment_title">Ask customer for KYC confirmation code</string>
    <string name="pin_failure_error_text_with_retry_count">This PIN code isn\\\'t correct. Please try again. \n You have [%1$d] attempts remaining</string>
    <string name="incorrect_pin_title_first_line">Incorrect password</string>
    <string name="incorrect_pin_title_second_line">Do not deliver</string>
    <string name="incorrect_pin_message_first_line">Politely inform the customer that you can not deliver the packages after 3 incorrect password attempts.</string>
    <string name="incorrect_pin_message_second_line">A new password will be sent and we will try delivering again tomorrow.</string>
    <string name="incorrect_pin_message_third_line">Please return packages to the station.</string>
    <string name="hello_blank_fragment">Hello blank fragment</string>
    <string name="pin_input_title">Password</string>
    <string name="pin_submit_button_text">Submit</string>
    <string name="retrigger_otp_text">Send OTP again</string>
    <string name="pin_failure_acknowledge_button_text">Acknowledge</string>
    <string name="pin_input_delivered_packages_summary_text">Total packages delivering: %1$d \nPackages delivering with OTP: %2$d out of %3$d</string>
    <string name="pin_input_prompt_text">Enter %1d digit one-time password</string>
    <string name="pin_input_attempts_text">You have [%1$d] attempt(s)</string>
    <string name="pin_failure_instruction_heading_text">Don\\\'t deliver these packages</string>
    <string name="pin_failure_instruction_note">The recipient does not have the correct OTP. Don\\\'t deliver these packages and continue to your other deliveries.</string>
    <string name="pin_failure_rejected_package_row_text">Barcode</string>
    <string name="pin_failure_error_text">This OTP isn\\\'t correct. Please try again. \nYou have %1$d attempt(s) remaining.</string>
    <string name="pin_failure_return_title">Unable to deliver because</string>
    <string name="pin_failure_return_reason">Cannot verify OTP</string>
    <string name="pvd_partial_delivery_message_text_1">You have not scanned the below package(s) which require OTP for delivery.</string>
    <string name="pvd_partial_delivery_message_text_2">Press \"Go Back\" to deliver/reject the shipment. If you press \"Continue Delivery\", you will not be able to reattempt these shipments without BTS.</string>
    <string name="pvd_partial_delivery_cancel_button_text">Go Back</string>
    <string name="pvd_partial_delivery_continue_button_text">Continue Delivery</string>
    <string name="otp_dialog_header_text">This package has a One-Time Password</string>
    <string name="dialog_subheader_text">Here\\\'s what to do:</string>
    <string name="dialog_button_text">Got it</string>
    <string name="help_instruction_text">Issues\? Use the Help menu</string>
    <string name="find_otp_instruction_text">Ask the customer to find the %1d-digit password in their &lt;![CDATA[&lt;span&gt;email&lt;/span&gt;]]&gt; (also shown in the Progress Tracker page)</string>
    <string name="otp_eg">e.g. &lt;![CDATA[&lt;span&gt;654321&lt;/span&gt;]]&gt;</string>
    <string name="hand_phone_instruction_text">Maintain a 6-foot distance from the &lt;![CDATA[&lt;span&gt;customer&lt;/span&gt;]]&gt; and ask them to read out the code</string>
    <string name="otp_dialog_clickable_text">&lt;![CDATA[&lt;span&gt;Tap here for info&lt;/span&gt;]]&gt; on where to \nfind one-time password</string>
    <string name="pin_verified_delivery_incorrect_pin_option_header">Incorrect or missing OTP</string>
    <string name="pin_verified_delivery_incorrect_pin_option_text">Mark this order as undeliverable. You\\\'ll return the item(s) to the station.</string>
    <string name="scan_unique_sealed_spoo_id">Scan unique sealed Bag Id</string>
    <string name="scan_feedback_duplicate_spoo_id">You\\\'ve already scanned this Bag Id.</string>
    <string name="scan_feedback_success_spoo_id">Scan Bag Id</string>
    <string name="scan_feedback_error_spoo_id">Wrong barcode. Scan the bag id</string>
    <string name="spoo_scan_activity_label">Tamper Proof Bag Scan</string>
    <string name="spoo_scan_title">SCAN</string>
    <string name="spoo_scan_overlay">%d of %d tamperproof bags scanned</string>
    <string name="spoo_package_id_type">Barcode</string>
    <string name="spoo_exception_flow_title">Select Exceptions</string>
    <string name="spoo_navi_title">Deliver packages securely</string>
    <string name="spoo_navi_message_1">Scan the tamper proof bag IDs.</string>
    <string name="spoo_navi_message_2">If you have trouble scanning a bag ID, a package has an incorrect bag, or a bag is not present, use &lt;b&gt;Deliver with exception&lt;/b&gt; in the Help menu.</string>
    <string name="spoo_navi_confirm_button">Got it</string>
    <string name="help_options_mark_exceptions_title">Mark exceptions</string>
    <string name="help_options_mark_exceptions_caption">Mark items which are either not present, not scannable, or are incorrect</string>
    <string name="payment_error_title">You have completed the mPOS transactions for the below tracking id(s). Please SWIPE TO FINISH these package(s) before initiating the delivery for remaining package(s). \n %s</string>
    <string name="payment_error_acknowledge">Acknowledge</string>
    <string name="call_customer_using_pin">%1$s is the pin to call customer</string>
    <string name="FLEX_WORK_ALERT_REMINDER_TITLE">Schedule Alert</string>
    <string name="FLEX_WORK_ALERT_REMINDER">You\\\'ve got a pickup in %1$s minutes.</string>
    <string name="NEW_INSTANT_OFFER_TITLE">Nearby offer ready now</string>
    <string name="NEW_INSTANT_OFFER">%1$s%2$s - %1$s%3$s including tips. Expires in %4$s secs.</string>
    <string name="NEW_INSTANT_OFFER_WITH_SURGE_TITLE">Instant Offer - %1$s%2$s - %1$s%3$s %4$s</string>
    <string name="NEW_INSTANT_OFFER_WITH_SURGE_TITLE_NO_TIPS">Instant Offer - %1$s%2$s %3$s</string>
    <string name="INSTANT_OFFER_FSM_TITLE">Grocery shopping and delivering - %1$s%2$s - %1$s%3$s %4$s</string>
    <string name="INSTANT_OFFER_FSM_TITLE_NO_TIPS">Grocery shopping and delivering - %1$s%2$s %3$s</string>
    <string name="IO_ACTIONABLE_NOTIFICATION_TITLE">Instant Offer - %1$s - %2$s %3$s</string>
    <string name="IO_ACTIONABLE_NOTIFICATION_TITLE_NO_TIPS">Instant Offer - %1$s %2$s</string>
    <string name="IO_ACTIONABLE_NOTIFICATION_PICKUP_DISTANCE_TEXT">Pick up %1$s away</string>
    <string name="IO_ACTIONABLE_NOTIFICATION_PICKUP_LOCATION_TEXT">(%1$s)</string>
    <string name="IO_ACTIONABLE_NOTIFICATION_PICKUP_TIME_TEXT">in %1$s.</string>
    <string name="EXPIRED_IO_NOTIFICATION_TITLE">Expired Instant Offer - %1$s - %2$s %3$s</string>
    <string name="EXPIRED_IO_NOTIFICATION_TITLE_NO_TIPS">Expired Instant Offer - %1$s %2$s</string>
    <string name="EXPIRED_IO_NOTIFICATION_TEXT">This offer is no longer available.</string>
    <string name="ACCEPT">ACCEPT</string>
    <string name="DECLINE">DECLINE</string>
    <string name="NEW_INSTANT_OFFER_WITH_SURGE">Accept within %1$s seconds.</string>
    <string name="io_notification_surge">Surge</string>
    <string name="flex_offer_confirmation_title">You got a block</string>
    <string name="flex_offer_confirmation_body">A block has been added to your schedule for %1$s at %2$s</string>
    <string name="user_confirmation_dialog_title">Confirm Call Recording</string>
    <string name="user_confirmation_dialog_message">Please note that your calls to Amazon customers may be recorded for quality and training purposes. \nTap OK to confirm.</string>
    <string name="internet_disabled_dialog_message">You will not be able to proceed since you don\\\'t have an active data connection. Please enable \"Mobile Data\" in mobile network settings or move to a location with better data network coverage.</string>
    <string name="delivery_box_skip_dialog_title">Are you Sure\?</string>
    <string name="delivery_box_skip_dialog_message">By skipping this step, you are confirming that the customer will be notified by other means and does not need a delivery box ID or access code.</string>
    <string name="delivery_box_activity_title">Delivery Box</string>
    <string name="delivery_box_before_finish_message">Please be sure that the above information is accurate, as this is exactly what will be sent to the customer.</string>
    <string name="skip_dialog_action_button">Skip</string>
    <string name="delivery_box_locker_info_label">Tell the customer to which delivery box you are delivering the package and its access code.</string>
    <string name="delivery_box_locker_id_label">Delivery Box Id</string>
    <string name="delivery_box_access_code_label">Delivery Box Access Code</string>
    <string name="delivery_box_no_locker_info_required_label">No delivery box information required\?</string>
    <string name="delivery_box_skip_step_label">Skip this step</string>
    <string name="wayfinding_scan_packages_button">Scan packages</string>
    <string name="wayfinding_at_locker_button">I\\\'m at the locker</string>
    <string name="wayfinding_continue_button">Continue</string>
    <string name="wayfinding_overview_title">Overview</string>
    <string name="wayfinding_overview_edit_stop_offline_alert_title">Your device is offline</string>
    <string name="wayfinding_overview_edit_stop_offline_alert_body">Connect to the internet to edit the stop.</string>
    <string name="wayfinding_overview_edit_stop_update_pending_alert_title">Please wait</string>
    <string name="wayfinding_overview_edit_stop_update_pending_alert_body">We’re still working on your previous edit.</string>
    <string name="wayfinding_deliver_title">Deliver</string>
    <string name="wayfinding_delivery_multiple_substops_same_building_dialog_title">Delivering multiple addresses to one location\?</string>
    <string name="wayfinding_delivery_multiple_substops_same_building_dialog_message">By selecting multiple deliveries to deliver at once you must be delivering to a mailroom, leasing office, receptionist, or doorman.</string>
    <string name="wayfinding_delivery_separate_buildings_dialog_title">Did you mean to select two separate addresses\?</string>
    <string name="wayfinding_delivery_separate_buildings_dialog_message">Two separate addresses should only be delivered together if they have the same leasing office, receptionist, or doorman. (e.g., 123 Main St. and 127 Main St.)</string>
    <string name="wayfinding_continue_delivery">Continue delivery</string>
    <string name="wayfinding_cancel">Cancel</string>
    <string name="wayfinding_alert_remove_packaging_title">We recommend taking the vest, gloves and box cutter to this stop</string>
    <string name="wayfinding_alert_remove_packaging_message">Don\\\'t unpack the delivery until the app prompts you to.</string>
    <string name="services_workflow_title">Deliver</string>
    <string name="send_text_button_text">Send SMS</string>
    <string name="custom_message_hint_text">Custom message</string>
    <string name="text_message_error_heading_text">Message not sent</string>
    <string name="text_message_error_text">This customer cannot receive SMS text messages. Please try calling the customer, or contact support.</string>
    <string name="text_message_error_call_customer">Call Customer %1$s</string>
    <string name="text_message_intro">Hi, my name is %1$s.</string>
    <string name="mailbox_delivery_warning_notification_title">Please do not deliver to mailbox</string>
    <string name="mailbox_delivery_warning_notification_description">It\\\'s illegal to deliver to a person\\\'s mailbox.</string>
    <string name="breaks_title">Breaks</string>
    <string name="breaks_menu_title">Breaks</string>
    <string name="breaks_menu_overdue">Break Time</string>
    <string name="break_ended_notification_title">Amazon Delivery</string>
    <string name="breaks_dismiss_button_dialog_text">Not right now</string>
    <string name="workhour_return_to_station">Return to station</string>
    <string name="workhour_message_exceeded_limit">You are about to hit your daily working hours limit. Please stop delivering packages and return to station.</string>
    <string name="workhour_label_travel_to_station">Travel to station</string>
    <string name="workhour_label_approaching_limit">Approaching Work Limit</string>
    <string name="workhour_message_approaching_limit">To make sure you\\\'re safe on the road, we will ask you to return to station before you hit the daily working hours limit.</string>
    <string name="workHour_limit_warning_description">Please be aware that you are close to hitting the daily working hours limit.</string>
    <string name="connect_to_station_wifi_dialog_title">Auto Connect to station Wi-Fi</string>
    <string name="connect_to_station_wifi_dialog_text">Do you want to automatically be connected to the station Wi-Fi when in vicinity\?</string>
    <string name="permissions_required_dialog_message">Please press \"ALLOW\" on the system dialog when asked</string>
    <string name="permissions_context_activity_title">App Setup</string>
    <string name="permissions_context_header">The following permissions are required to deliver with Amazon Flex:</string>
    <string name="permissions_location_name">Current Location</string>
    <string name="permissions_location_reason">To guide you from place to place</string>
    <string name="permissions_camera_name">Camera Access</string>
    <string name="permissions_camera_reason">To scan packages for pickup and delivery</string>
    <string name="permissions_phone_name">Phone State</string>
    <string name="permissions_phone_reason">To verify phone number and activity</string>
    <string name="permissions_storage_name">Storage</string>
    <string name="permissions_storage_reason">To download and install EzeTapPayment</string>
    <string name="permissions_call_phone_name">Call Phone</string>
    <string name="permissions_call_phone_reason">To auto-dial phone numbers</string>
    <string name="permissions_call_log_name">Call Log</string>
    <string name="permissions_call_log_reason">To verify customer call attempts</string>
    <string name="permissions_launch_settings">Enable in Settings</string>
    <string name="permissions_denied_header">Please toggle the following permissions ON in Settings to deliver with Amazon Flex:</string>
    <string name="kyc_reminder_header">&lt;b&gt;This customer requires KYC verification&lt;/b&gt;</string>
    <string name="kyc_reminder_text">Remind customer that KYC verification is pending and can be completed now. &lt;![CDATA[&lt;span&gt;Start KYC verification&lt;/span&gt;]]&gt;</string>
    <string name="long_running_notification_body">You\\\'re on duty.</string>
    <string name="package_not_in_route_error_secondary_title">This package is not for this route</string>
    <string name="package_not_in_route_error_message">Do not pickup this package. Please hand it over to an Amazon associate.</string>
    <string name="package_id_literal">Package ID</string>
    <string name="change_locale_title">APP LANGUAGE</string>
    <string name="language_submit_button_text">Select</string>
    <string name="language_update_success">App language updated to %1$s. You can update it again in &lt;![CDATA[&lt;span&gt;settings&lt;/span&gt;]]&gt;.</string>
    <string name="change_language_info">You can now change the app language</string>
    <string name="wait_for_activity_update_title">Please Wait</string>
    <string name="route_staging_notification">You\\\'ll get a notification when your packages are ready</string>
    <string name="route_staging_title">Sorry, your packages aren\\\'t quite ready yet</string>
    <string name="route_unblocked_notification_message">Your packages are ready for pickup. Tap here to begin.</string>
    <string name="route_service_notification_message">You will be notified when your route is ready for pickup.</string>
    <string name="crates_activity_title">Crates</string>
    <string name="bottles_activity_title">Bottles</string>
    <string name="crates_subtitle_text">Only add crates here, you\\\'ll add bottles separately next</string>
    <string name="bottles_subtitle_text">Add bottles</string>
    <string name="default_primary_button_text">Continue</string>
    <string name="bottle_deposit_information_text">Swipe to continue to complete delivery and initiate bottle pickup.</string>
    <string name="pick_up_bottles_dialog_primary_button_text">No, next stop</string>
    <string name="pick_up_bottles_dialog_secondary_button_text">Yes, accept deposit</string>
    <string name="pick_up_bottles_dialog_title_text">Please ask the customer now if they have bottles or crates to return.</string>
    <string name="pick_up_bottles_dialog_body_text">Your delivery is complete, but ask the customer if they have crates or bottles for pick up. If they do, select \"Yes\" below.</string>
    <string name="bottle_deposit_help_options_call_dispatcher">Call Support</string>
    <string name="deposit_item_success_notification">Bottle/Crate Pickup completed</string>
    <string name="deposit_item_failure_notification">Failed to save refund, please retry pickup</string>
    <string name="deposit_item_summary_title">Summary</string>
    <string name="deposit_item_summary_reason_header">Picked up from</string>
    <string name="rts_completed_notification">All returned items have been unloaded at the station successfully</string>
    <string name="deposit_item_workflow_completed_dialog_title">Your customer\\\'s deposit items have been picked up</string>
    <string name="deposit_item_workflow_completed_dialog_body">You\\\'ll need to unload the deposit items at the station at the end of your deliveries. We\\\'ve added a return to station stop to your itinerary.</string>
    <string name="liveness_check_intro_dialog_title">Identity Check</string>
    <string name="liveness_check_intro_dialog_message_1">To verify your identity you\\\'ll be asked to follow on-screen instructions.</string>
    <string name="liveness_check_intro_dialog_message_3">Data and images captured during this check will not be shared with customers.</string>
    <string name="liveness_check_intro_dialog_message_2">Do not drive during this check.</string>
    <string name="liveness_check_intimation_dialog_button">Begin</string>
    <string name="liveness_check_do_not_move_instruction">Do not drive during this check</string>
    <string name="liveness_check_completed">Thank you!</string>
    <string name="liveness_check_instruction_tilt_left">Tilt left</string>
    <string name="liveness_check_instruction_tilt_right">Tilt right</string>
    <string name="liveness_check_instruction_head_straight">Head straight</string>
    <string name="liveness_check_instruction_smile">Smile</string>
    <string name="liveness_check_instruction_no_smile">Don\\\'t smile</string>
    <string name="liveness_check_instruction_eyes_open">Eyes open</string>
    <string name="liveness_check_face_not_found_error_message">Keep your face well lit and visible within the frame.</string>
    <string name="liveness_check_multiple_faces_error_message">Too many faces within the frame.</string>
    <string name="liveness_check_submitting">Submitting</string>
    <string name="liveness_check_help_caption">Having trouble with face detection.</string>
    <string name="sentinel_screen_title">Verify Identity</string>
    <string name="blocked_sentinel_waiting_title">Please wait while we verify your identity</string>
    <string name="blocked_sentinel_waiting_message">This should only take about 30 seconds.</string>
    <string name="blocked_sentinel_rejected_title">We can\\\'t verify your identity with the image on file</string>
    <string name="blocked_sentinel_rejected_message">Please ask a Station Associate for manual a identity check.</string>
    <string name="blocked_sentinel_rejected_message_gsf">Please call Support for assistance.</string>
    <string name="blocked_sentinel_rejected_primary_button_gsf">Call Support</string>
    <string name="blocked_sentinel_rejected_primary_button">Station Associate verified me</string>
    <string name="blocked_sentinel_retry_message_title">Photo does not match your image on file.</string>
    <string name="blocked_sentinel_retry_message">Please try again.</string>
    <string name="safety_app_check_dialog_title">Aarogya Setu App Required</string>
    <string name="safety_app_check_dialog_body_text1">As recommended by the Government of India, please install the Aarogya Setu app. Please actively use it for your safety.</string>
    <string name="safety_app_check_dialog_positive_button">Install Aarogya Setu</string>
    <string name="health_check_homescreen_instructions">Safety is our top priority. Ensure your temperature is below %1$s before arriving for deliveries every day.</string>
    <string name="health_check_dialog_title">Safety is our top priority</string>
    <string name="health_check_default_temperature_value">100.4°F / 38°C</string>
    <string name="health_reminder_header">Remember that you &lt;b&gt;should not make deliveries&lt;/b&gt; if any of these are true:</string>
    <string name="health_reminder_dry_cough">• You have a dry cough, shortness of breath, difficulty breathing, chills, muscle pain, sore throat, or new loss of taste or smell</string>
    <string name="health_reminder_international_travel">• You traveled outside of the country in the last 14 days</string>
    <string name="health_reminder_human_contact">• You have been in close contact with someone with COVID-19 in the last 14 days</string>
    <string name="health_reminder_self_isolation">• You are in a 14-day period of self-isolation as directed by a healthcare provider or health official</string>
    <string name="health_reminder_footer">If any of these are true, please do not continue to deliver and instead contact your DSP or Driver Support.</string>
    <string name="health_reminder_sick_header">The health and safety of everyone in our operations and community is our top priority.</string>
    <string name="health_reminder_sick_fever">Your answer indicates that &lt;b&gt;you may have a fever&lt;/b&gt;.</string>
    <string name="health_reminder_sick_preventative_measure">As a preventative measure, please &lt;b&gt;contact your doctor and stay home&lt;/b&gt; until you have been fever-free for at least 72 hours, without the use of fever-reducing medicine.</string>
    <string name="health_reminder_covid_positive">If you are diagnosed with COVID-19, please contact us immediately so that we can support you. Thank you.</string>
    <string name="health_reminder_sick_footer">We will be &lt;b&gt;cancelling any scheduled deliveries&lt;/b&gt; for today.</string>
    <string name="health_check_acknowledgement">OK</string>
    <string name="health_reminder_acknowledgement">Got it</string>
    <string name="health_check_dialog_message">Did you take your temperature &lt;b&gt;today&lt;/b&gt; and is it &lt;b&gt;below&lt;/b&gt; %1$s\?</string>
    <string name="health_check_dialog_exception_text">*Local Exceptions</string>
    <string name="review_items_activity_title">REVIEW ITEMS</string>
    <string name="review_items_activity_subtitle">Open the box and review each item.\nSelect items to reject.</string>
    <string name="review_items_continue_button_text">Deliver %1s items</string>
    <string name="review_items_continue_button_reject_item_text">Deliver %1s, Reject %1s items</string>
    <string name="review_items_continue_button_reject_all_item_text">Reject all items</string>
    <string name="review_items_reject_count_summary">Reject %1s of %1s</string>
    <string name="review_items_rejected_item_count_text">%1s Rejected</string>
    <string name="return_reason_dialog_title">Reject items</string>
    <string name="return_reason_dialog_subtitle">&lt;b&gt;Reject how many, and why\?&lt;/b&gt;</string>
    <string name="return_reason_cancel_button_text">Cancel</string>
    <string name="return_reason_item_incorrect_string">Different from what was Ordered</string>
    <string name="return_reason_item_missing_string">Missing</string>
    <string name="return_reason_item_no_longer_wanted_string">No Longer Wanted</string>
    <string name="return_reason_item_damaged_string">Damaged</string>
    <string name="return_reason_other_string">Other</string>
    <string name="review_items_navi_dialog_title">Review items</string>
    <string name="review_items_navi_dialog_body">Open the box and review each item with the customer. You can reject individual items right from the app</string>
    <string name="review_items_navi_dialog_button_text">Got it</string>
    <string name="attended_pod_dialog_header_text">May require photo on delivery</string>
    <string name="attended_pod_dialog_body_text">If the customer is near the package, make sure identifiable features like faces or tattoos are not in the photo.</string>
    <string name="review_items_select_text">Select item</string>
    <string name="check_in_state_machine_error_message">Something went wrong. Please try again.</string>
    <string name="check_in_state_machine_non_retry_error_message">Something went wrong, please contact Driver Support</string>
    <string name="helper_details_title">You have a helper</string>
    <string name="helper_details_instruction">Check-in helper by entering their Amazon alias below.</string>
    <string name="helper_details_alias_text">Helper\\\'s Amazon alias</string>
    <string name="helper_details_invalid_alias_text">Invalid alias</string>
    <string name="helper_not_scheduled_error_text">This helper hasn\\\'t been scheduled to work today by your DSP. Please contact your DSP and check-in another helper.</string>
    <string name="helper_session_exists_error_text">This helper has been checked-in by another driver. Please check-in another helper</string>
    <string name="helper_details_network_error_text">There\\\'s no internet connectivity. Please try again when you\\\'re online.</string>
    <string name="helper_details_gateway_error_text">Sorry, something went wrong. Please try again later.</string>
    <string name="help_option_unable_to_checkin_helper">Unable to check-in helper</string>
    <string name="helper_checkin_exception_subtitle">What\\\'s the problem\?</string>
    <string name="helper_checkin_exception_cant_find_helper">I can\\\'t find my helper/my helper isn\\\'t here</string>
    <string name="helper_checkin_exception_unable_to_checkin_helper">I have a helper, but I\\\'m unable to check them in</string>
    <string name="linked_checkin_complete_notification_banner">Helper has been checked in</string>
    <string name="linked_checkin_failed_notification_banner">Helper wasn\\\'t checked in</string>
    <string name="partner_take_break_acknowledgement_driver_role">I acknowledge that the helper is with me and ready to take break at this time</string>
    <string name="partner_take_break_acknowledgement_helper_role">I acknowledge that the driver is with me and ready to take break at this time</string>
    <string name="helper_checkin_title">Helper Check-in</string>
    <string name="helper_checkin_primary_button_title">Check-in Helper</string>
    <string name="helper_checkin_instruction_title">Who\\\'s helping you today\?</string>
    <string name="route_partners_helper_checkin_title">Route Partners</string>
    <string name="route_partners_helper_checkin_instruction_title">You haven\\\'t checked in any helper on this route</string>
    <string name="helper_checkin_instruction">Scan the QR code displayed on the helper\\\'s device to check them in. If you can\\\'t find the helper, talk to your DSP.</string>
    <string name="help_option_skip_helper_checkin">Can\\\'t find helper, skip check-in</string>
    <string name="helper_checkin_reminder_headline">You haven\\\'t checked in your helper</string>
    <string name="helper_checkin_reminder_body">This route requires a helper. Please check them in before leaving the station.\n\nIf you can\\\'t find the helper assigned to you, talk to your DSP.</string>
    <string name="helper_checkin_reminder_primary_button_text">Check-in helper</string>
    <string name="helper_checkin_reminder_secondary_button_text">Continue to delivery</string>
    <string name="helper_scan_title">Scan</string>
    <string name="helper_scan_instruction">Scan the QR code or &lt;![CDATA[&lt;span&gt;enter the numeric code&lt;/span&gt;]]&gt; provided by the helper</string>
    <string name="helper_info_title">Helper %d</string>
    <string name="helper_info_caption">Check-in code scanned at %s</string>
    <string name="helper_info_time_format">hh:mm a</string>
    <string name="helper_info_remove_button">Remove</string>
    <string name="helper_scan_primary_button_title">Complete Check-in</string>
    <string name="helper_scan_invalid_qr_error">Invalid QR code scanned</string>
    <string name="helper_scan_helper_checkin_limit_error">You can\\\'t check-in another helper for this route. Remove one and try again.</string>
    <string name="helper_checkin_internet_connection_error">You aren\\\'t connected to the internet. Please try again when you\\\'re online.</string>
    <string name="helper_checkin_generic_error">Sorry, there was a problem. Try again or talk to your DSP.</string>
    <string name="helper_checkin_generic_error_title">Sorry, there was a problem</string>
    <string name="helper_checkin_generic_error_message">Make sure you are connected to the internet and try again.</string>
    <string name="helper_checkin_error_alert_button_text">Try again</string>
    <string name="manual_helper_code_input_toolbar_title">Enter Helper Code</string>
    <string name="manual_helper_code_input_title">Enter the numeric code provided by the helper</string>
    <string name="manual_helper_code_input_edit_text_hint">Example: 123456</string>
    <string name="manual_helper_code_input_primary_button_text">Submit</string>
    <string name="helper_qr_gen_instruction_title">Check-in with the driver</string>
    <string name="helper_qr_gen_instruction">Have the driver scan the QR code below. Once you have been checked-in, you\\\'ll be taken to the next screen.</string>
    <string name="helper_qr_gen_caption_text">&lt;b&gt;Can\\\'t scan\?&lt;/b&gt;\nEnter code: </string>
    <string name="helper_checkin_complete_notification_banner">You are checked in</string>
    <string name="helper_checkin_unavailable_notification_banner">Helper routes (Ironhide) are not available at your station. Please contact the OTR manager for next steps.</string>
    <string name="helper_checkin_issue_with_helper">Issue with helper</string>
    <string name="helper_checkin_pair_with_driver">Pair with driver</string>
    <string name="continue_to_next_stop_header">Prepare packages for the next stop</string>
    <string name="continue_to_next_stop_body">While the helper is completing this stop, you can save time by getting packages ready for the next stop.</string>
    <string name="continue_to_next_stop_primary_button">See next stop</string>
    <string name="continue_to_next_stop_secondary_button">Dismiss</string>
    <string name="group_stops_navi_dialog_title">Splitting up the locations may help you deliver faster</string>
    <string name="group_stops_navi_body">• If you can\\\'t find the parking, you should stay with the vehicle. \n• Consider whether it would be more efficient to divide the locations between you and helper(s).</string>
    <string name="message_received_notification">Message received</string>
    <string name="inapp_message_new_message_notification_header">You got a messsage!</string>
    <string name="fsm_pickup_view_open_shopping_app">Open Shopping App</string>
    <string name="fsm_pickup_initial_view_title">Time to shop</string>
    <string name="fsm_pickup_initial_view_message">&lt;![CDATA[• Go inside the store and tap the button below&lt;br /&gt;• Sign into Shopping app with your Amazon Flex account&lt;br /&gt;• Shop for the items on the list, bag and scan them]]&gt;</string>
    <string name="fsm_pickup_app_not_installed_title">Download the shopping app to continue</string>
    <string name="fsm_pickup_app_not_installed_message">&lt;![CDATA[• Tap &lt;font color=#FF7817&gt;here&lt;/font&gt; to download the shopping app&lt;br /&gt;• Sign in with your Flex account]]&gt;</string>
    <string name="fsm_pickup_still_shopping_view_title">Still shopping\?</string>
    <string name="fsm_pickup_still_shopping_view_message">&lt;![CDATA[Finish shopping for your order before coming back to Amazon Flex.&lt;br /&gt;If you are done shopping, tap Done Shopping.]]&gt;</string>
    <string name="fsm_pickup_order_pending_view_title">Wait, we are prepping your order</string>
    <string name="fsm_pickup_order_pending_view_message">&lt;![CDATA[This screen should automatically refresh when your order is ready. It may take up to one minute. You can also tap Refresh to check if it is ready.&lt;br /&gt;&lt;br /&gt;If you aren\\\'t done shopping, tap Continue Shopping.]]&gt;</string>
    <string name="fsm_pickup_order_ready_view_title">Continue to scan packages on Amazon Flex</string>
    <string name="fsm_pickup_order_ready_view_message">&lt;![CDATA[You\\\'re done with the Shopping app.&lt;br /&gt;Now scan the bags using the Amazon Flex app. Once you\\\'re done, you\\\'ll deliver them.]]&gt;</string>
    <string name="fsm_pickup_view_continue_shopping">Continue Shopping</string>
    <string name="fsm_pickup_view_done_shopping">Done Shopping</string>
    <string name="fsm_pickup_view_refresh">Refresh</string>
    <string name="fsm_pickup_view_scan_packages">Scan Packages</string>
    <string name="confirmation_dialog_title">Complete item review</string>
    <string name="confirmation_dialog_body">Ensure that all the items are reviewed with the customer before proceeding. Post completion, you won’t be able to edit item status.</string>
    <string name="confirmation_dialog_complete_button_text">Complete Review</string>
    <string name="confirmation_dialog_cancel_button_text">Cancel</string>
    <string name="dase_end_work_modal_title">End Work</string>
    <string name="dase_end_work_modal_message">Make sure you complete all of your work before hitting \\\'End work.\\\'</string>
    <string name="dase_end_work_modal_confirm">End work</string>
    <string name="dase_end_work_modal_cancel">Continue work</string>
    <string name="itinerary_overview_title">Hi %1$s, welcome to your first route!</string>
    <string name="day_string">Today</string>
    <string name="route_Firsts_Title_Prefix">Heads up! </string>
    <string name="route_Firsts_Title">You have a few firsts at these stops. We will help you when you get there.</string>
    <string name="stop_count_label">Stops</string>
    <string name="package_count_label">Packages</string>
    <string name="itinerary_overview_button_title">Got it</string>
    <string name="itinerary_overview_stop_label">Stop</string>
    <string name="itinerary_list_location_expander_label">Locations</string>
    <string name="rf_grouped_stop">Grouped stop</string>
    <string name="rf_recipient_required">Recipient required</string>
    <string name="rf_get_signature">Get signature</string>
    <string name="rf_scheduled">Scheduled</string>
    <string name="rf_verify_age">Verify age</string>
    <string name="rf_verify_otp">Verify OTP</string>
    <string name="rf_collect_cash">Collect cash</string>
    <string name="rf_return_packages">Return packages</string>
    <string name="rf_in_home_delivery">In-home delivery</string>
    <string name="rf_in_garage_delivery">In-garage delivery</string>
    <string documentation="-" name="rf_in_one_click_access">1-Click Access</string>
    <string name="rf_locker_delivery">Locker delivery</string>
    <string name="rf_buy_back_packages">Buyback packages</string>
    <string name="rf_open_box_delivery">Open box delivery</string>
    <string name="rf_pantry_delivery">Pantry delivery</string>
    <string name="rf_in_box_delivery">Secure Box</string>
    <string name="rf_pickup">Pickup</string>
    <string name="rf_residential_complex">Residential complex</string>
    <string name="rf_commercial_building">Commercial building</string>
    <string name="rf_mixed_use_building">Mixed-use building</string>
    <string name="rf_more_than_x_packages">More than 5 packages</string>
    <string name="enforcement_speeding_banner_label">Slow down</string>
    <string name="enforcement_ftux_speedlimit_title">Drive at safe speed</string>
    <string name="enforcement_ftux_speedlimit_description">Stay updated on speed limits during your route. It\\\'s your responsibility to drive safely according to the law and follow all local signage.</string>
    <string name="enforcement_ftux_next_button">Next</string>
    <string name="enforcement_ftux_dismiss_button">Got it</string>
    <string name="enforcement_ftux_driver_feedback_title">Your safe driving habits help everyone</string>
    <string name="enforcement_ftux_driver_feedback_description">We\\\'ll provide reminders if we detect potentially unsafe behaviors. If a device has connectivity issues, some alerts may be delayed.</string>
    <string name="block_generic_pickup_dialog_title">Pick up not allowed</string>
    <string name="block_generic_pickup_dialog_description">This item can be picked only if it is assigned to you and scanned via the current stop workflow</string>
    <string name="txt_acknowledge">Acknowledge</string>
    <string name="payment_summary_success_tr_list_title">Parcels paid</string>
    <string name="payment_summary_pending_tr_list_title">Parcels needing payment</string>
    <string name="payment_summary_call_to_action_no_pending_trs">Please deliver the above parcels.</string>
    <string name="payment_summary_call_to_action_with_pending_trs">Review the transaction before collecting payment and delivering the parcel(s).</string>
    <string name="handle_with_care_scan_feedback_msg">Handle with care</string>
    <string name="handle_with_care_dialog_title">Handle with care</string>
    <string name="handle_with_care_first_ftux_dialog_title">Some packages may contain hazardous materials\n\nUse the icon in the app to identify these packages</string>
    <string name="handle_with_care_first_ftux_dialog_detail">Some packages in your route may contain hazardous materials such as lithium ion batteries or essential oils.\n\nThese packages will be marked with an icon. Tap the icon to see the hazmat category of these items.</string>
    <string name="handle_with_care_second_ftux_dialog_title">Handle hazmat packages with care</string>
    <string name="handle_with_care_second_ftux_dialog_detail">Packages may also have labels indicating they contain hazardous materials.Handle them gently to keep yourself and customers safe.\n\nIf you have questions about these packages, speak with a station associate.</string>
    <string name="cart_scan_pickup_ftux_pubr_success_title">All packages scanned</string>
    <string name="cart_scan_pickup_ftux_pubr_success_detail">You\\\'ve just scanned everything on your route. You\\\'re ready to load your vehicle and start delivering.</string>
    <string name="commingled_ssd_pickup_ftux_success_title">First set of packages scanned!</string>
    <string name="commingled_ssd_pickup_ftux_success_detail">You\\’ve scanned the first set of packages on your route. You may proceed to load your vehicle and return to pick up the rest of the packages.</string>
    <string name="cart_scan_pickup_ftux_gsf_intro_title">Scan the cart\\\'s barcode</string>
    <string name="cart_scan_pickup_ftux_gsf_intro_detail">You can pick up all packages on a cart by scanning the cart\\\'s attached barcode.\n\nIf the barcode is inaccessible or damaged, you can pick up everything on the cart by scanning a package instead.</string>
    <string name="cart_scan_pickup_ftux_amzl_intro_title_page1">Scan each cart\\\'s QR code</string>
    <string name="cart_scan_pickup_ftux_amzl_intro_detail_page1">Pick up everything on a cart by scanning the cart\\\'s QR code sticker.</string>
    <string name="cart_scan_pickup_ftux_amzl_intro_title_page2">Identify your carts</string>
    <string name="cart_scan_pickup_ftux_amzl_intro_detail_page2">The app’s pickup screen will show you the last 3 digits of each cart\\\'s ID number. You can find the number near the QR code on the end of each cart.</string>
    <string name="cart_scan_pickup_ftux_amzl_intro_title_page3">Pick up carts in order</string>
    <string name="cart_scan_pickup_ftux_amzl_intro_detail_page3">Scan and load the carts in the order they\\\'re listed on the scan screen.</string>
    <string name="cart_scan_pickup_success_ftux_title">Entire cart scanned</string>
    <string name="cart_scan_pickup_success_ftux_detail">You\\\'ve scanned everything on this cart. Now scan any other carts assigned to you.</string>
    <string name="cart_scan_pickup_success_alert_detail">Scan any other carts assigned to you.</string>
    <string name="cart_scan_pickup_summary_unknown">Unknown</string>
    <string name="cart_scan_pickup_error_guidance_title">Scan each cart to pick up</string>
    <string name="cart_scan_pickup_error_guidance_details">Scan a cart\\\'s QR code to pick up all items on the cart. If you\\\'re unable to scan the cart code, select an alternate option in the Help menu or speak with a station associate.</string>
    <string name="cart_scan_pickup_error_guidance_continue">Continue</string>
    <string name="options_enter_cart_id_header">Manually enter cart ID</string>
    <string name="manual_cart_id_entry_title_activity">Enter ID</string>
    <string name="manual_cart_id_barcode_entry_title">Enter the cart ID without dashes starting after CRT/PAL/CART.</string>
    <string name="manual_cart_id_barcode_entry_hint">ex. 1133MPQ</string>
    <string name="local_rush_retail_instructions">Instructions</string>
    <string name="cart_scan_pickup_title_activity">SCAN CARTS</string>
    <string name="local_rush_retail_return_title">You\\\'ll return this package to the store</string>
    <string name="local_rush_retail_return_body">At the end of your route you\\\'ll go back to the store to return this package.</string>
    <string name="local_rush_retail_return_button">Got it</string>
    <string name="local_rush_retail_return_notification">Thanks for returning the items.</string>
    <string name="local_rush_retail_store_closing_dialog_title">This store is closing soon</string>
    <string name="local_rush_retail_store_closing_dialog_body">%1$s closes at %2$s. If you can\\\'t get to the store safely within 30 minutes of closing, please return packages by 2&amp;#160;PM tomorrow.</string>
    <string name="local_rush_retail_store_closing_dialog_button">Return now</string>
    <string name="local_rush_retail_store_closed_dialog_title">This store is closed</string>
    <string name="local_rush_retail_store_closed_dialog_body">Please return packages by 2&amp;#160;PM tomorrow.</string>
    <string name="local_rush_retail_store_closed_dialog_button">Return tomorrow</string>
    <string name="local_rush_retail_store_next_day_opening_hours">%1$s opens at %2$s.</string>
    <string name="local_rush_retail_store_next_day_reminder">We\\\'ll send you a reminder tomorrow by 10 AM.</string>
    <string name="local_rush_retail_next_day_returns_header_singlestore">You can tap on the store\\\'s address to navigate there. Once you\\\'ve arrived, follow the instructions to return the packages. Instructions will also be posted at the store.</string>
    <string name="local_rush_retail_next_day_returns_header_multistore">You can tap on a store’s address to navigate there. Once you\\\'ve arrived, follow the instructions to return the packages by 2 PM. Instructions will also be posted at the stores.</string>
    <string name="local_rush_retail_next_day_returns_notification_title">Return packages by 2 PM today</string>
    <string name="local_rush_retail_next_day_returns_notification_body">Please return packages to %1$s by 2 PM. Tap for details.</string>
    <string name="local_rush_retail_next_day_returns_opening_hours">Opens at %1$s</string>
    <string name="local_rush_retail_next_day_returns_secondary_button">Remind me in 1 hour</string>
    <string name="local_rush_retail_next_day_returns_delay_picker_header">Choose the notification delay (in seconds)</string>
    <string name="business_hours_photo_capture_instructions">If there are posted business hours, step back and capture them in the frame.\n\nNo hours\? &lt;![CDATA[&lt;span&gt;Skip&lt;/span&gt;]]&gt;</string>
    <string name="CarouselViewNext">Next</string>
    <string name="sds_webview_title">Support</string>
    <string name="connection_title_notification">Thank you!</string>
    <string name="connection_sub_title_notification">We\\\'ll use your feedback to improve your experience.</string>
    <string name="bunny_activity_title">Connected Vehicle</string>
    <string name="offer_transfer_t_and_c">The Amazon Flex terms and conditions for independent freight forwarders apply.</string>
    <string name="photo_issue">Photo Issue</string>
    <string name="photo_bypass_title">Unable to take a photo\?</string>
    <string name="photo_bypass_instructions">Please provide more information about why you are unable to take a photo of the delivery.</string>
    <string name="photo_bypass_prompt">Enter reason here</string>
    <string name="photo_bypass_submit">Submit</string>
    <string name="photo_upload_retry_message">Please try again.</string>
    <string name="stop_detail_verify_otp">•   Verify OTP</string>
    <string name="substop_requirement_verify_otp">One-time password</string>
    <string name="duration_deliver_to">You can deliver until:</string>
    <string name="swa_container_title">Shipper Pickup Container</string>
    <string name="shipper_pickup_container_stop_arrival_scan_button_text">Pick up bags</string>
    <string name="shipper_pickup_container_stop_arrival_title_text">Pick up bags at this shipper</string>
    <string name="shipper_pickup_container_summary_toolbar_title_text">Summary</string>
    <string name="shipper_pickup_container_summary_title_text">Picked up and dropped off at</string>
    <string name="shipper_pickup_container_summary_picked_up_header">Picked up</string>
    <string name="shipper_pickup_container_summary_dropped_off_header">Dropped off</string>
    <string name="loading_guidance_activity_title">LOAD</string>
    <string name="loading_guidance_continue">Continue</string>
    <string name="loading_guidance_single_stop">Stop %1$d</string>
    <string name="loading_guidance_stop_range">Stops %1$d-%2$d</string>
    <string name="loading_guidance_ov_package_section_title">Overflow packages</string>
    <string name="loading_guidance_title">Load your vehicle</string>
    <string name="loading_guidance_warning_message">Return to the scan screen to finish scanning the remaining carts.</string>
    <string name="loading_guidance_warning_message_for_dispatch_stops">All carts are staged. Return to the scan screen to finish scanning the remaining carts.</string>
    <string name="loading_guidance_warning_title_message">Some carts are not ready</string>
    <string name="loading_guidance_warning_cta_message">Return to itinerary</string>
    <string name="loading_guidance_warning_unstaged_message">Carts will become available as they are staged. You\'ll need to go back to the itinerary and refresh it to see new carts.</string>
    <string name="loading_guidance_carts_will_be_ready_shortly">Carts will be ready shortly</string>
    <string name="loading_guidance_ascending_sort_title">First to last stop</string>
    <string name="loading_guidance_descending_sort_title">Last to first stop</string>
    <string name="loading_guidance_ascending_route_sort_title">First to last route</string>
    <string name="loading_guidance_descending_route_sort_title">Last to first route</string>
    <string name="order_id_title">Order ID</string>
    <string name="exchange_summary_delivery_title">Delivered and picked up from</string>
    <string name="local_rush_retail_app_chooser_title">Choose an app for walking directions to the store. Not all apps have walking directions for all stores.</string>
    <string name="local_rush_retail_pickup_primary_button">I\\\'m at the store</string>
    <string name="local_rush_retail_pickup_packages">Pick up packages</string>
    <string name="local_rush_retail_pickup_secondary_button">Get walking directions</string>
    <string name="auto_selected_reason_code_deliver_title">Deliver to</string>
    <string name="amxl_co_sdarp_late_delivery">You have a scheduled delivery at risk of being delivered late going to %1$s. Please ensure this order is delivered within the promise time window. Please Contact your DSP manager if you have any other questions. Thank you</string>
    <string name="amxl_co_sdarp_late_return">You have a scheduled customer return pickup at risk of being picked up late going to %1$s. Please ensure this order is picked up and package is marked in the device during the promise window. Please Contact your DSP manager if you have any other questions. Thank you</string>
    <string name="amxl_co_sdarp_early_delivery">You have a scheduled delivery at risk of being delivered early going to %1$s. Please ensure this order is delivered within the promise time window. Please Contact your DSP manager if you have any other questions. Thank you</string>
    <string name="amxl_co_sdarp_early_return">You have a scheduled customer return pickup at risk of being picked up early going to %1$s. Please ensure this order is picked up and package is marked in the device during the promise window. Please Contact your DSP manager if you have any other questions. Thank you</string>
    <string name="driver_license_verification_body_title">Verify your drivers license</string>
    <string name="driver_license_verification_body_message">Once verified, this screen will close, and you can begin pickup.</string>
    <string name="driver_license_verification_how_to_verify_button_text">How to verify</string>
    <string name="driver_license_verification_how_to_verify_popup_text">How to verify your license</string>
    <string name="digital_manifest_free_replacement">Free replacement</string>
    <string name="digital_manifest_high_value">Valuable</string>
    <string documentation="Title for the permission screen during login" name="permissions">Permissions</string>
    <string documentation="Title for the permission screen during login" name="route_scan_guide_index_1">1.</string>
    <string documentation="Title for the permission screen during login" name="route_scan_guide_index_2">2.</string>
    <string documentation="Title for the permission screen during login" name="route_scan_guide_index_3">3.</string>
    <string documentation="Title for the permission screen during login" name="route_scan_guide_no_routes_available_button_title">No route available\?</string>
    <string name="account_display_theme">Display theme</string>
    <string name="account_display_theme_automatic">Automatic</string>
    <string name="account_display_theme_light">Light</string>
    <string name="account_display_theme_dark">Dark</string>
    <string name="account_display_theme_automatic_hint">Same as device settings</string>
    <string name="account_display_theme_light_hint">Light mode is always on</string>
    <string name="account_display_theme_dark_hint">Dark mode is always on</string>
    <string name="loading_guidance_bags_header">(Bags)</string>
    <string name="cart_loading_guidance_title">Load your vehicle in the order listed</string>
    <string name="loading_guidance_ovpackages_header">(Overflow packages)</string>
    <string name="waste_signature_activity_title">Signature</string>
    <string name="e_waste_try_different_container">Please try a different container. If the issue persists, contact station manager</string>
    <string name="commingled_route_ready_header">Route includes groceries</string>
    <string name="commingled_route_ready_footer">After you load your vehicle with the first set of packages, you\'ll go back inside the station to pick up the perishable items.</string>
    <string name="commingled_route_ready_action_button">Continue</string>
    <string name="commingled_route_ready_perishables">\nGroceries</string>
    <string name="customer_pickup_package_generic_label">Return Label</string>
    <string name="gsf_aa_wait_screen_title">Finding a route, please wait</string>
    <string name="gsf_aa_wait_screen_message">We’ll send you a notification when your route is ready. Please stay in your vehicle.</string>
    <string name="gsf_aa_prepare_screen_title">Preparing your route, please wait</string>
    <string name="gsf_aa_prepare_screen_message">We’ve assigned you a route and are preparing your packages. You will receive a notification when your route is ready for pickup.</string>
    <string name="gsf_aa_wait_screen_toolbar_title">PLEASE WAIT</string>
    <string name="gsf_aa_wait_screen_progress_find_route">Finding a route</string>
    <string name="gsf_aa_wait_screen_progress_prepare_route">Preparing your route</string>
    <string name="gsf_aa_wait_screen_progress_ready_pickup">Ready for pickup</string>
    <string name="sunset_screen_announcement_title">We moved!</string>
    <string name="sunset_screen_expiration_date">This app expires %1$s.</string>
    <string name="sunset_screen_announcement_subtitle">Get the new version on Google Play to keep delivering.</string>
    <string name="sunset_screen_final_warning_title">New app alert!</string>
    <string name="sunset_screen_final_warning_subtitle">Download on Google Play now to keep delivering.</string>
    <string name="sunset_screen_eol_title">This app has expired.</string>
    <string name="sunset_screen_eol_subtitle">To keep delivering, you\\\'ll need to download the new version on Google Play now.</string>
    <string name="sunset_exit_app_button">Exit App</string>
    <string name="sunset_screen_delete_title">Uninstall this app now</string>
    <string name="sunset_screen_delete_subtitle">This app has expired. You can uninstall it in app settings.</string>
    <string name="open_app_settings_button">Open app settings</string>
    <string name="vehicle_details_header">Vehicle Details</string>
    <string documentation="Primary action button for CaptureSignatureView" name="capture_signature_done_button">Done</string>
    <string documentation="Secondary action button for CaptureSignatureView" name="capture_signature_clear_button">Clear</string>
    <string documentation="To show tag for RVP suppressed pickup" name="no_verification">No Verification</string>
    <string name="permissions_overlay">Overlay Permissions</string>
    <string documentation="To request user to hit a toggle in permissions page of settings" name="permissions_overlay_reason">&lt;![CDATA[Your device requires the &lt;b&gt;Display over other apps&lt;/b&gt; permission to run Amazon Flex.  Please tap Continue, select Amazon Flex, and then tap the toggle beside &lt;b&gt;Allow display over other apps.&lt;/b&gt;]]&gt;</string>
    <string documentation="Requesting user to update their device's software" name="device_firmware_not_eligible_warning_title">Update system software</string>
    <string documentation="After the given date, users will not be able to deliver unless they update their software" name="device_firmware_not_eligible_warning_reason">Beginning %1$s, this app will no longer run on %2$s %3$s with device software version %4$s.</string>
    <string documentation="Instructions for how to update system software" name="device_firmware_not_eligible_warning_solution">To continue delivering, go to this device\\\'s Settings to upgrade to the latest version.</string>
    <string documentation="Fully blocking the user from delivering until they update their software" name="device_firmware_not_eligible_blocking_title">This device software is no longer supported</string>
    <string name="device_firmware_not_eligible_blocking_reason">This app no longer supports devices running on %1$s %2$s with device software version %3$s.</string>
    <string name="device_firmware_not_eligible_blocking_solution">To continue delivering, go to this device\\\'s Settings to upgrade to the latest version.</string>
    <string documentation="Title for a screen that requests user to upgrade to a new device" name="device_model_not_eligible_warning_title">Upgrade to a new device</string>
    <string name="device_model_not_eligible_warning_reason">Beginning %1$s, this app will no longer run on the %2$s %3$s.</string>
    <string name="device_model_not_eligible_warning_solution">To continue delivering, upgrade your device to meet minimum requirements published on the Amazon Flex website.</string>
    <string documentation="Title for a screen that tells user their device is not supported" name="device_model_not_eligible_blocking_title">This device is no longer supported</string>
    <string documentation="Blocking a user's device model" name="device_model_not_eligible_blocking_reason">This app no longer supports the %1$s %2$s.</string>
    <string name="device_model_not_eligible_blocking_solution">To continue delivering, upgrade your device to meet minimum requirements published on the Amazon Flex website.</string>
    <string documentation="Title for a screen that tells user their device manufacturer is not supported in the future" name="device_manufacturer_not_eligible_warning_title">Upgrade to a new device</string>
    <string documentation="Blocking a user's device manufacturer" name="device_manufacturer_not_eligible_warning_reason">Beginning %1$s, this app will no longer run on %2$s devices.</string>
    <string documentation="Directing user to Amazon flex website to see min spec requirements" name="device_manufacturer_not_eligible_warning_solution">To continue delivering, upgrade your device to meet minimum requirements published on the Amazon Flex website.</string>
    <string documentation="Title for a screen that tells user their device manufacturer is not supported" name="device_manufacturer_not_eligible_blocking_title">This device is no longer supported</string>
    <string documentation="App no longer supports given manufacturer" name="device_manufacturer_not_eligible_blocking_reason">This app no longer supports %1$s devices.</string>
    <string documentation="Directing user to Amazon flex website to see min spec requirements" name="device_manufacturer_not_eligible_blocking_solution">To continue delivering, upgrade your device to meet minimum requirements published on the Amazon Flex website.</string>
    <string documentation="Title for a screen that tells user update their Android OS" name="device_os_not_eligible_warning_title">Update to Android %1$s or newer</string>
    <string name="device_os_not_eligible_warning_reason">Beginning %1$s, this app will no longer support devices running Android versions below %2$s.</string>
    <string name="device_os_not_eligible_warning_solution">To continue delivering, please update your device to Android version %1$s or above.</string>
    <string name="device_os_not_eligible_blocking_title">Update to Android %1$s or newer</string>
    <string name="device_os_not_eligible_blocking_reason">This app no longer supports devices running Android versions below %1$s.</string>
    <string name="device_os_not_eligible_blocking_solution">To continue delivering, please update your device to Android version %1$s or above.</string>
    <string name="tr_object_reason_code_cubeout_subheader">Which package(s) are not scanned</string>
    <string name="nfc_scan_early_check_in_description">If you’re here early, please come back later so other drivers can check in now.</string>
    <string name="nfc_scan_checkin_successfully">Check-in successful</string>
    <string name="nfc_scan_proceed_to_parking_title">Please proceed to parking</string>
    <string name="nfc_scan_proceed_to_parking_description">After parking, walk inside the station to pick up your route.</string>
    <string name="nfc_scan_assigning_route_loader_text">Assigning a route...</string>
</resources>

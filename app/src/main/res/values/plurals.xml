<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<resources>
    <plurals name="scan_remove_orders">
        <item quantity="one">{param}Remove %1$d Order</item>
        <item quantity="other">{param}Remove %1$d Orders</item>
    </plurals>
    <plurals name="scan_continue_with_x_packages">
        <item quantity="one">{param}Continue with %d package</item>
        <item quantity="other">{param}Continue with %d packages</item>
    </plurals>
    <plurals name="packages_for_customer">
        <item quantity="one">{param}%1$d package for %2$s</item>
        <item quantity="other">{param}%1$d packages for %2$s</item>
    </plurals>
    <plurals name="packages_from">
        <item quantity="one">{param}%1$d package from %2$s</item>
        <item quantity="other">{param}%1$d packages from %2$s</item>
    </plurals>
    <plurals name="delivery_details_view_rejected_items">
        <item quantity="one">{param}View %1$d returned item</item>
        <item quantity="other">{param}View %1$d returned items</item>
    </plurals>
    <plurals name="package_information">
        <item quantity="one">{param}%1$d package for pickup</item>
        <item quantity="other">{param}%1$d packages for pickup</item>
    </plurals>
    <plurals name="notification_new_stops">
        <item quantity="one">{param}You have %1$d new stop</item>
        <item quantity="other">{param}You have %1$d new stops</item>
    </plurals>
    <plurals name="notification_new_assignments">
        <item quantity="one">{param}You have %1$d new assignment</item>
        <item quantity="other">{param}You have %1$d new assignments</item>
    </plurals>
    <plurals name="availability_set_hours">
        <item quantity="one">{param}You\\\'ve set %1$d hour of available time.</item>
        <item quantity="other">{param}You\\\'ve set %1$d hours of available time.</item>
    </plurals>
    <plurals name="packages_scanned">
        <item quantity="one">{param}%1$d package scanned. If you have more, continue scanning.</item>
        <item quantity="other">{param}%1$d packages scanned. If you have more, continue scanning.</item>
    </plurals>
    <plurals name="packages_scanned_without_additional_text">
        <item quantity="one">{param}%1$d package scanned</item>
        <item quantity="other">{param}%1$d packages scanned</item>
    </plurals>
    <plurals name="verify_packages_button_with_package_count_text">
        <item quantity="one">{param}Verify %1$d package</item>
        <item quantity="other">{param}Verify %1$d packages</item>
    </plurals>
    <plurals name="packages_scan_to_continue">
        <item quantity="one">{param}Scan %1$d package to continue</item>
        <item quantity="other">{param}Scan %1$d packages to continue</item>
    </plurals>
    <plurals name="return_reason_subtext_damaged">
        <item quantity="one">{param}You\\\'ve marked %1$d package as damaged</item>
        <item quantity="other">{param}You\\\'ve marked %1$d packages as damaged</item>
    </plurals>
    <plurals name="return_reason_subtext_incorrect">
        <item quantity="one">{param}You\\\'ve marked %1$d item as incorrect</item>
        <item quantity="other">{param}You\\\'ve marked %1$d items as incorrect</item>
    </plurals>
    <plurals name="return_reason_subtext_items_missing">
        <item quantity="one">{param}You\\\'ve marked %1$d item as missing</item>
        <item quantity="other">{param}You\\\'ve marked %1$d items as missing</item>
    </plurals>
    <plurals name="return_reason_subtext_packages_missing">
        <item quantity="one">{param}You\\\'ve marked %1$d package as missing</item>
        <item quantity="other">{param}You\\\'ve marked %1$d packages as missing</item>
    </plurals>
    <plurals name="return_reason_subtext_unwanted">
        <item quantity="one">{param}You\\\'ve marked %1$d item as unwanted</item>
        <item quantity="other">{param}You\\\'ve marked %1$d items as unwanted</item>
    </plurals>
    <plurals name="return_reason_subtext_age_restricted">
        <item quantity="one">{param}You\\\'ve marked $1$d item as age verification failed</item>
        <item quantity="other">{param}You\\\'ve marked $1$d items as age verification failed</item>
    </plurals>
    <plurals name="do_not_pickup_package">
        <item quantity="zero">{param}Don\\\'t pick up this order</item>
        <item quantity="one">{param}Don\\\'t pick up this order</item>
        <item quantity="other">{param}Don\\\'t pick up these orders</item>
    </plurals>
    <plurals name="do_not_deliver_package">
        <item quantity="one">{param}Don\\\'t deliver this package</item>
        <item quantity="other">{param}Don\\\'t deliver these packages</item>
    </plurals>
    <plurals name="scan_error_header_canceled_order">
        <item quantity="one">{param}This order has been canceled</item>
        <item quantity="other">{param}These orders have been canceled</item>
    </plurals>
    <plurals name="scan_error_message_continue_pickup">
        <item quantity="one">{param}Leave the package with an Amazon representative. Continue picking up any remaining packages.</item>
        <item quantity="other">{param}Leave these packages with an Amazon representative. Continue picking up any remaining packages.</item>
    </plurals>
    <plurals name="pickup_error_header_unable_to_assign">
        <item quantity="one">{param}Unable to assign package</item>
        <item quantity="other">{param}Unable to assign packages</item>
    </plurals>
    <plurals name="pickup_error_message_leave_packages">
        <item quantity="one">{param}Leave the following package with an Amazon representative.</item>
        <item quantity="other">{param}Leave the following packages with an Amazon representative.</item>
    </plurals>
    <plurals name="work_selection_duration_in_hours">
        <item quantity="one">{param}%1$d HR</item>
        <item quantity="other">{param}%1$d HRS</item>
    </plurals>
    <plurals name="verify_packages_button_text">
        <item quantity="one">{param}Verify %d package</item>
        <item quantity="other">{param}Verify %d packages</item>
    </plurals>
    <plurals name="scan_packages_button_text">
        <item quantity="one">{param}Scan %d package</item>
        <item quantity="other">{param}Scan %d packages</item>
    </plurals>
    <plurals name="delivery_scanless_stop_arrival_primary_button_text">
        <item quantity="one">{param}Review order (%d package)</item>
        <item quantity="other">{param}Review order (%d packages)</item>
    </plurals>
    <plurals name="group_delivery_packages_button_text">
        <item quantity="one">{param}Deliver</item>
        <item quantity="other">{param}Deliver %d together</item>
    </plurals>
    <plurals name="pickup_scanless_stop_review_primary_button_text">
        <item quantity="one">{param}Continue (%d package)</item>
        <item quantity="other">{param}Continue (%d packages)</item>
    </plurals>
    <plurals name="calendar_detail_scheduled_times">
        <item quantity="one">{param}%1$d scheduled time</item>
        <item quantity="other">{param}%1$d scheduled times</item>
    </plurals>
    <plurals name="new_stops_dialog_header">
        <item quantity="one">{param}You have %1$d new stop</item>
        <item quantity="other">{param}You have %1$d new stops</item>
    </plurals>
    <plurals name="dsp_home_stops_present_instruction_text">
        <item quantity="one">{param}You currently have %1$d stop left to complete.</item>
        <item quantity="other">{param}You currently have %1$d stops left to complete.</item>
    </plurals>
    <plurals name="package_exceptions">
        <item quantity="one">{param}%1$d item is marked as \\\"%2$s\\\"</item>
        <item quantity="other">{param}%1$d items are marked as \\\"%2$s\\\"</item>
    </plurals>
    <plurals name="stop_count">
        <item quantity="one">{param}%d stop</item>
        <item quantity="other">{param}%d stops</item>
    </plurals>
    <plurals name="group_stop_count_info">
        <item quantity="one">{param}Includes %d grouped stop</item>
        <item quantity="other">{param}Includes %d grouped stops</item>
    </plurals>
    <plurals name="multi_location_stop_count_info">
        <item quantity="one">{param}Includes %d multi-location stop</item>
        <item quantity="other">{param}Includes %d multi-location stops</item>
    </plurals>
    <plurals name="number_of_deliveries">
        <item quantity="one">{param}%1$d Delivery</item>
        <item quantity="other">{param}%1$d Deliveries</item>
    </plurals>
    <plurals name="number_of_pickups">
        <item quantity="one">{param}%1$d Pickup</item>
        <item quantity="other">{param}%1$d Pickups</item>
    </plurals>
    <plurals name="number_of_packages">
        <item quantity="one">{param}%1$d Package</item>
        <item quantity="other">{param}%1$d Packages</item>
    </plurals>
    <plurals name="options_report_package_missing">
        <item quantity="one">{param}Report package as missing</item>
        <item quantity="other">{param}Report packages as missing</item>
    </plurals>
    <plurals name="number_of_hours">
        <item quantity="one">{param}%1$d Hr</item>
        <item quantity="other">{param}%1$d Hrs</item>
    </plurals>
    <plurals name="hour_full_text">
        <item quantity="one">{param}%1$s Hour</item>
        <item quantity="other">{param}%1$s Hour</item>
    </plurals>
    <plurals name="earnings_detail_tips_pending_time_frame">
        <item quantity="one">{param}Tips pending and will be confirmed in about %1$d hour</item>
        <item quantity="other">{param}Tips pending and will be confirmed in about %1$d hours</item>
    </plurals>
    <plurals name="characters_remaining_count">
        <item quantity="one">{param}%1$d character remaining</item>
        <item quantity="other">{param}%1$d characters remaining</item>
    </plurals>
    <plurals name="scanned_package_count">
        <item quantity="one">{param}You\\\'ve already scanned %1$d package.</item>
        <item quantity="other">{param}You\\\'ve already scanned %1$d packages.</item>
    </plurals>
    <plurals name="stop_detail_delivery_requirement">
        <item quantity="one">{param}Delivery requirement</item>
        <item quantity="other">{param}Delivery requirements</item>
    </plurals>
    <plurals name="pubr_stop_detail_package_count">
        <item quantity="one">{param}%1$d package picked up</item>
        <item quantity="other">{param}%1$d packages picked up</item>
    </plurals>
    <plurals name="stop_detail_package_count">
        <item quantity="one">{param}%1$d package</item>
        <item quantity="other">{param}%1$d packages</item>
    </plurals>
    <plurals name="stop_detail_package_remaining_count">
        <item quantity="one">{param}+ %1$d more package</item>
        <item quantity="other">{param}+ %1$d more packages</item>
    </plurals>
    <plurals name="stop_detail_item_count">
        <item quantity="one">{param}%1$d item</item>
        <item quantity="other">{param}%1$d items</item>
    </plurals>
    <plurals name="lockers_recipient_confirmation_header_text">
        <item quantity="one">{param}Delivered %1$d package to the Locker</item>
        <item quantity="other">{param}Delivered %1$d packages to the Locker</item>
    </plurals>
    <plurals name="lockers_pickup_recipient_confirmation_header_text">
        <item quantity="one">{param}Picked up %1$d package from the Locker</item>
        <item quantity="other">{param}Picked up %1$d packages from the Locker</item>
    </plurals>
    <plurals name="lockers_undeliverable_recipient_confirmation_text">
        <item quantity="one">{param}%1$d package undelivered</item>
        <item quantity="other">{param}%1$d packages undelivered</item>
    </plurals>
    <plurals name="lockers_package_delivery_summary">
        <item quantity="one">{param}%1$d package not included in Locker checkout.</item>
        <item quantity="other">{param}%1$d packages not included in Locker checkout.</item>
    </plurals>
    <plurals name="lockers_package_delivery_instructions">
        <item quantity="one">{param}Go to &lt;b&gt; Help &lt;/b&gt; &gt; &lt;b&gt;Return items&lt;/b&gt; to mark %1$d package as missing or damaged.</item>
        <item quantity="other">{param}Go to &lt;b&gt;Help&lt;/b&gt; &gt; &lt;b&gt;Return items&lt;/b&gt; to mark packages as missing or damaged.</item>
    </plurals>
    <plurals name="pickup_count_text">
        <item quantity="one">{param}Pick up %1$d package</item>
        <item quantity="other">{param}Pick up %1$d packages</item>
    </plurals>
    <plurals name="lockers_skip_to_stop_dialog">
        <item quantity="one">{param}You may have up to %1$d package from earlier stops in your itinerary that can\\\'t be delivered and will need to go to a Locker.\n\nIf you go to the Locker now, you may need to return to the Locker later.</item>
        <item quantity="other">{param}You may have up to %1$d packages from earlier stops in your itinerary that can\\\'t be delivered and will need to go to a Locker.\n\nIf you go to the Locker now, you may need to return to the Locker later.</item>
    </plurals>
    <plurals name="lockers_unpicked_up_recipient_confirmation_text">
        <item quantity="one">{param}Unable to pick up %1$d package at the locker</item>
        <item quantity="other">{param}Unable to pick up %1$d of %2$d packages at the locker</item>
    </plurals>
    <plurals name="lockers_itinerary_change_dialog_title">
        <item quantity="one">{param}PACKAGE REDIRECTED</item>
        <item quantity="other">{param}PACKAGES REDIRECTED</item>
    </plurals>
    <plurals name="lockers_itinerary_change_dialog">
        <item quantity="one">{param}You\\\'ll deliver the package you set aside at a later stop.&lt;br/&gt; %2$s</item>
        <item quantity="other">{param}You\\\'ll deliver the %1$d packages you set aside at a later stop.&lt;br/&gt; %2$s</item>
    </plurals>
    <plurals name="avd_error_page_partial_delivery_header">
        <item quantity="one">{param}Don\\\'t deliver %1$d package below</item>
        <item quantity="other">{param}Don\\\'t deliver the packages below</item>
    </plurals>
    <plurals name="avd_error_page_partial_delivery_subheader">
        <item quantity="one">{param}The recipient does not meet the age requirements. Keep %1$d package and continue to your other deliveries.</item>
        <item quantity="other">{param}The recipient does not meet the age requirements. Keep the packages and continue to your other deliveries.</item>
    </plurals>
    <plurals name="avd_error_page_partial_delivery_expired_license_subheader">
        <item quantity="one">{param}The recipient\\\'s ID is expired, and is no longer valid. Keep %1$d package and continue to your other deliveries.</item>
        <item quantity="other">{param}The recipient\\\'s ID is expired, and is no longer valid. Keep the packages and continue to your other deliveries.</item>
    </plurals>
    <plurals name="verification_code_sms_arrival_timer">
        <item quantity="one">{param}Your SMS should arrive within %1$d second.</item>
        <item quantity="other">{param}Your SMS should arrive within %1$d seconds.</item>
    </plurals>
    <plurals name="otp_sms_arrival_timer">
        <item quantity="one">{param}Customer should receive the OTP within %1$d second.</item>
        <item quantity="other">{param}Customer should receive the OTP within %1$d seconds.</item>
    </plurals>
    <plurals name="io_detail_pickup_instructions_label">
        <item quantity="one">{param}Pick up in %1$d minute, deliver immediately.</item>
        <item quantity="other">{param}Pick up in %1$d minutes, deliver immediately.</item>
    </plurals>
    <plurals name="io_estimated_distance_label">
        <item quantity="one">{param}%1$.1f kilometer</item>
        <item quantity="other">{param}%1$.1f kilometers</item>
    </plurals>
    <plurals name="io_estimated_time_label">
        <item quantity="one">{param}~%1$d minute</item>
        <item quantity="other">{param}~%1$d minutes</item>
    </plurals>
    <plurals name="io_time_label">
        <item quantity="one">{param}%1$d minute</item>
        <item quantity="other">{param}%1$d minutes</item>
    </plurals>
    <plurals name="distance_unit_mile">
        <item quantity="one">{param}%1$.1f mile</item>
        <item quantity="other">{param}%1$.1f miles</item>
    </plurals>
    <plurals name="crates_primary_button">
        <item quantity="one">{param}Continue with %d crate</item>
        <item quantity="other">{param}Continue with %d crates</item>
    </plurals>
    <plurals name="bottles_primary_button">
        <item quantity="one">{param}Continue with %d bottle</item>
        <item quantity="other">{param}Continue with %d bottles</item>
    </plurals>
    <plurals name="return_items_summary_crates_header">
        <item quantity="one">{param}Return %s crate</item>
        <item quantity="other">{param}Return %s crates</item>
    </plurals>
    <plurals name="return_items_summary_bottles_header">
        <item quantity="one">{param}Return %s bottle</item>
        <item quantity="other">{param}Return %s bottles</item>
    </plurals>
    <plurals name="return_items_summary_packages_header">
        <item quantity="one">{param}Return %s package to origin</item>
        <item quantity="other">{param}Return %s packages to origin</item>
    </plurals>
    <plurals name="countdown_time_remaining">
        <item quantity="one">{param}%1$d second remaining</item>
        <item quantity="other">{param}%1$d seconds remaining</item>
    </plurals>
    <plurals name="distance_unit_km">
        <item quantity="one">{param}%1$.1f kilometer</item>
        <item quantity="other">{param}%1$.1f kilometers</item>
    </plurals>
    <plurals name="distance_unit_m">
        <item quantity="one">{param}%1$.1f meter</item>
        <item quantity="other">{param}%1$.1f meters</item>
    </plurals>
    <plurals name="packages_for">
        <item quantity="one">{param}%1$d package for</item>
        <item quantity="other">{param}%1$d packages for</item>
    </plurals>
    <plurals name="scheduling_number_of_offers">
        <item quantity="one">{param}%1$d Offer</item>
        <item quantity="other">{param}%1$d Offers</item>
    </plurals>
    <plurals name="scheduling_offer_delivery_request_count">
        <item quantity="one">{param}(Up to %1$d order)</item>
        <item quantity="other">{param}(Up to %1$d orders)</item>
    </plurals>
    <plurals name="scheduling_single_offer_delivery_request_count">
        <item quantity="one">{param}By scheduling this block, I agree to complete up to %1$d Orders, which is %2$s Delivery Request. Each Order may contain multiple packages.</item>
        <item quantity="other">{param}By scheduling this block, I agree to complete up to %1$d Orders, which is %2$s Delivery Requests. Each Order may contain multiple packages.</item>
    </plurals>
    <plurals name="scheduling_offers_filter_by_orders_count_text">
        <item quantity="one">{param}Up to %1$d Orders (%2$d Delivery Request)</item>
        <item quantity="other">{param}Up to %1$d Orders (%2$d Delivery Requests)</item>
    </plurals>
    <plurals name="scheduling_number_of_filtered_offers">
        <item quantity="one">{param}%1$d of %2$d Offer</item>
        <item quantity="other">{param}%1$d of %2$d Offers</item>
    </plurals>
    <plurals name="number_of_packages_picked_up">
        <item quantity="one">{param}%d package picked up</item>
        <item quantity="other">{param}%d packages picked up</item>
    </plurals>
    <plurals name="package_exception_header">
        <item quantity="one">{param}%1$d removed package requires Amazon approval</item>
        <item quantity="other">{param}%1$d removed packages require Amazon approval</item>
    </plurals>
    <plurals name="stop_details_canceled_return_to_station_headline">
        <item quantity="one">{param}Do not deliver. Return this package to the station.</item>
        <item quantity="other">{param}Do not deliver. Return these %d packages to the station.</item>
    </plurals>
    <plurals name="stop_details_canceled_no_return_headline">
        <item quantity="one">{param}Do not deliver this package.</item>
        <item quantity="other">{param}Do not deliver these %d packages.</item>
    </plurals>
    <plurals name="remove_packages_from_route">
        <item quantity="one">{param}Removed %d package from your route</item>
        <item quantity="other">{param}Removed %d packages from your route</item>
    </plurals>
    <plurals name="permissions_rationale_title">
        <item quantity="one">{param}%s is required to deliver with Amazon</item>
        <item quantity="other">{param}%s are required to deliver with Amazon</item>
    </plurals>
    <plurals name="total_package_summary">
        <item quantity="zero">{param}Packages delivered</item>
        <item quantity="one">{param}Package delivered</item>
        <item quantity="other">{param}Packages delivered</item>
    </plurals>
    <plurals name="spoo_bag_success_summary">
        <item quantity="zero">{param}Bags delivered</item>
        <item quantity="one">{param}Bag delivered</item>
        <item quantity="other">{param}Bags delivered</item>
    </plurals>
    <plurals name="spoo_bag_exceptions_summary">
        <item quantity="zero">{param}Bags delivered with exception</item>
        <item quantity="one">{param}Bag delivered with exception</item>
        <item quantity="other">{param}Bags delivered with exception</item>
    </plurals>
    <plurals name="spoo_scan_overlay">
        <item quantity="one">{param}%d of %d tamperproof bag scanned</item>
        <item quantity="other">{param}%d of %d tamperproof bags scanned</item>
    </plurals>
    <plurals name="spoo_exceptions_header">
        <item quantity="one">{param}Confirm that the following package could not be scanned</item>
        <item quantity="other">{param}Confirm that the following packages could not be scanned</item>
    </plurals>
    <plurals name="acknowledge_button_text">
        <item quantity="one">{param}Acknowledge %d package</item>
        <item quantity="other">{param}Acknowledge %d packages</item>
    </plurals>
    <plurals name="scan_continue_button_progress_text">
        <item quantity="one">{param}Continue with %d package</item>
        <item quantity="other">{param}Continue with %d packages</item>
    </plurals>
    <plurals name="vehicle_inspection_checklist_issues">
        <item quantity="one">{param}REPORT %d ISSUE</item>
        <item quantity="other">{param}REPORT %d ISSUES</item>
    </plurals>
    <plurals name="helpers_scanned_count">
        <item quantity="one">{param}%d check-in code scanned</item>
        <item quantity="other">{param}%d check-in codes scanned</item>
    </plurals>
    <plurals name="local_rush_retail_return_items_summary_packages_header">
        <item quantity="one">{param}Return %s item</item>
        <item quantity="other">{param}Return %s items</item>
    </plurals>
    <plurals name="home_today">
        <item quantity="one">{param}Today</item>
        <item quantity="other">{param}Today (%d of %d)</item>
    </plurals>
    <plurals name="home_tomorrow">
        <item quantity="one">{param}Tomorrow</item>
        <item quantity="other">{param}Tomorrow (%d of %d)</item>
    </plurals>
    <plurals name="shipper_pickup_container_summary_containers_picked_up">
        <item quantity="one">{param}%d Bag</item>
        <item quantity="other">{param}%d Bags</item>
    </plurals>
    <plurals name="shipper_pickup_container_summary_packages_picked_up">
        <item quantity="one">{param}%d Package</item>
        <item quantity="other">{param}%d Packages</item>
    </plurals>
    <plurals name="shipper_pickup_container_summary_containers_dropped_off">
        <item quantity="one">{param}%d Empty bag</item>
        <item quantity="other">{param}%d Empty bags</item>
    </plurals>
    <plurals name="itinerary_list_location_packages_count">
        <item quantity="one">Deliver 1 package</item>
        <item quantity="other">Deliver {param}%d packages</item>
    </plurals>
    <plurals name="itinerary_summary_group_stops_count">
        <item quantity="one">There is 1 grouped stop.</item>
        <item quantity="other">There are {param}%d grouped stops.</item>
    </plurals>
    <plurals name="itinerary_summary_multi_location_stops_count">
        <item quantity="one">There is 1 multi-location stop.</item>
        <item quantity="other">There are {param}%d multi-location stops.</item>
    </plurals>
    <plurals name="digital_manifest_itinerary_expander_title">
        <item quantity="one">1 item</item>
        <item quantity="other">{param}%d items</item>
    </plurals>
    <plurals name="loading_guidance_missing_bags">
        <item quantity="one">1 missing bag</item>
        <item quantity="other">{param}%d missing bags</item>
    </plurals>
    <plurals name="loading_guidance_missing_ovs">
        <item quantity="one">1 missing overflow package</item>
        <item quantity="other">{param}%d missing overflow packages</item>
    </plurals>
    <plurals name="scan_error_header_canceled_rescheduled_order_for_hub_da">
        <item quantity="one">This order has been canceled or rescheduled</item>
        <item quantity="other">These orders have been canceled or rescheduled</item>
    </plurals>
    <plurals name="scan_error_message_continue_pickup_for_hub_da">
        <item quantity="one">Return this package to your Hub location. The Hub App will contain further instructions.</item>
        <item quantity="other">Return these packages to your Hub location. The Hub App will contain further instructions.</item>
    </plurals>
    <plurals name="stop_details_canceled_reschedule_return_to_hub_headline">
        <item quantity="one">Do not deliver. Return this package to your Hub location. The Hub App will contain further instructions.</item>
        <item quantity="other">Do not deliver. Return these %d packages to your Hub location. The Hub App will contain further instructions.</item>
    </plurals>
</resources>

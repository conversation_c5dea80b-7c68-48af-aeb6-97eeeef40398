<FindBugsFilter>

    <!--Sample Spotbugs/Fingbugs File
    Will be changed specific to Last Mile during Peru migration. -->

    <!-- By default, findbugs looks at class files generated by <PERSON><PERSON><PERSON>. Findbugs is only designed to
    work on class files generated by java code, so we are excluding them here -->
    <Match>
        <Source name="~.*\.kt"/>
    </Match>
    <!-- TODO: TRIX-480: Fix/Resolve Findbugs violations when using ButterKnife 7.x-->
    <Match>
        <Bug code="UR, UrF"/>
    </Match>
    <Match>
        <Class name="~.*R2\$.*"/>
    </Match>

</FindBugsFilter>

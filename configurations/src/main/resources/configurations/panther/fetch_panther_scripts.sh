#!/bin/bash

set +e
mkdir panther_tmp
cd panther_tmp
git archive --format=tar --remote=ssh://git.amazon.com:2222/pkg/RabbitAndroidPantherScripts mainline | tar -xf -
cp src/rabbit_android_panther_scripts/panther_scripts/lib/androidLocaleFormatter.py ../src/main/panther_scripts/lib/
cp src/rabbit_android_panther_scripts/panther_scripts/lib/preImport.py ../src/main/panther_scripts/lib/
cp src/rabbit_android_panther_scripts/panther_scripts/lib/postImport.py ../src/main/panther_scripts/lib/
cd ..
rm -rf panther_tmp
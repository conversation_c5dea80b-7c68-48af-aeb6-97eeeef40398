#!/bin/bash

set -e

CURRENT_DIR=$(pwd)
ROOT_PROJECT_DIR=$(pwd)/$(dirname "${BASH_SOURCE[0]}")/../../..
PACKAGE_DIR=$(pwd)/../..
echo $CURRENT_DIR
echo $ROOT_PROJECT_DIR
echo $PACKAGE_DIR
changed_files=$(git diff --relative --name-only HEAD~2)
removed_prefix='<!--DO NOT MODIFY MANUALLY:'
removed_suffix='-->'
newline=$'\n'

echo "$changed_files"

if [[ "$changed_files" == *"strings.xml"* ]]; then
	echo "strings.xml file(s) updated, checking for manual edits"
	changed_file_list=(${changed_files/ / })

	string_dir_list=()
	for filename in "${changed_file_list[@]}"
	do
	   filepath=$CURRENT_DIR/$filename
	   if [[ "$filename" != *"strings.xml"* ]]; then
	   	continue
	   fi
	   echo "Checking $filename"
	   md5=$(tail -1 $filepath)
	   if [[ $md5 != *"$removed_prefix"* ]]; then
	   	continue
	   fi
	   md5=${md5#"$removed_prefix"}
	   md5=${md5%"$removed_suffix"}
	   # echo "md5"
	   # echo $md5

	   file_content=$(cat $filepath)
	   file_content=${file_content%"$newline$removed_prefix$md5$removed_suffix"}

	   # 	echo "file_content"
	   # echo "${file_content}"

	   md5path=/sbin/md5
	   expectedMd5=''
	   if test -f "$md5path"; then
	   	expectedMd5=$(echo -n "${file_content}" | md5)
	   else 
	   	expectedMd5=$(echo -n "${file_content}" | md5sum | cut -d' ' -f1)
	   fi
	   # 	echo "expectedMd5"
	   # echo $expectedMd5

	   if [ "$expectedMd5" = "$md5" ]; then
	   	echo "$filename was not modified. Continue"
	   else
	   	echo "$filename was modified manually. Exit 1"
	   	exit 1
	   fi
	done
fi
echo "Done"



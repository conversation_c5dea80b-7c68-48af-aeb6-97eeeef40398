# built application files
*.apk
*.ap_
build/
captures/
 
# files for the dex VM
*.dex
 
# Java files
*.class
*.hprof
 
# generated files
bin/
gen/
build
 
# Local configuration file (sdk path, etc)
local.properties
 
# Eclipse project files
.classpath
.project
 
# Proguard folder generated by Eclipse
proguard/
mapping.txt
seeds.txt
unused.txt
 
# Intellij project files
*.iml
*.ipr
*.iws
.idea/
*.hpof
 
gradle
.gradle/
.settings/
.DS_Store
 
local.properties
generated-src/
 
# Added by GradleTrails
/buildSrc/**
!/buildSrc/build.gradle
 
 

# Generated by run-gradlew
/gradle/wrapper/gradle-wrapper.properties
/gradle/wrapper/gradle-wrapper.jar

# Panther translation files (generated by panther translate)
src/main/sourcemessages/native/translations/

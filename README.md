# LastMileBuildComponent (LMBC)

LMBC standardizes Gradle builds across all Last Mile Feature Packages for easier and consistent
developer experience. This package contains plugins that Last Mile Feature Packages will apply and
get default build behavior.

## Highlights

### Convention Plugins

Convention Plugins are extremely powerful and are opinionated on the tasks that are applied. These
plugins are less flexible for configurations and thrives for same build behaviour.

### LastMileGradlePlatform (LMGP)

[LMGP](https://code.amazon.com/packages/LastMileGradlePlatform/trees/mainline) declares 1p/2p/3p
dependency versions that Last Mile feature packages need.

## Integration

Convention Plugins will be made available through LMBC `build.gradle`

```
gradlePlugin {
   plugins {
      lastMileAndroidAppPlugin {
            id = 'com.amazon.lastmile.android.app'
            displayName = 'LastMile Android App Plugin'
            implementationClass = 'com.amazon.lastmile.buildcomponent.convention.android.LastMileAndroidAppPlugin'
            description = 'Gradle Plugin applied for Android Applications.'
      }
   }
}      
```

Last Mile Feature Package need to apply package related convention plugin

Example for Android Library:

`build.gradle`

```
plugins {
    id 'com.amazon.lastmile.android.lib' version '1.0+'
}

group = "com.amazon.rabbit.example"
version = "1.0.0"
```

If you use multiple Gradle submodules and don't want to repeat the `group` and `version` in each build file, then you
can set the these values in the project's `gradle.properties` file, which will apply them to all submodules:
```
GROUP=com.amazon.rabbit.example
VERSION_NAME=1.0.0
```

📝 In a subproject setup, apply `version` only at the root-level and LMBC classpath will be
defined at the root-level.

Example for Android Root Project:

`build.gradle`

```
plugins {
	id 'com.amazon.lastmile.android.project' version '1.0+'
}
```

In the successive plugins, **do not** apply plugin with `version`. Below is an example on how to
apply:

Android App `build.gradle`

```
plugins {
    id 'com.amazon.lastmile.android.app'
}
```

⚠️ In-case, version is applied at multiple plugins, gradle errors with
`> The request for this plugin could not be satisfied because the plugin is already on the classpath with an unknown version, so compatibility cannot be checked.`
To fix this, remove the `version` at downstream dependencies.

## Build Order

LMBC need to be built before Last Mile Feature Package, so the below explanation will connect the
execution dots between Last Mile Feature Package, LMBC and LMGP
when `brazil-recursive-cmd --allPackages brazil-build` is executed.

In Last Mile Feature Package `brazil.ion`

```
 dependencies: {
    ...

    build_after: [
      "LastMileBuildComponent"
    ]
  }
```

In LMBC `brazil.ion`

```
 dependencies: {
     ...

    build_after: [
      "LastMileGradlePlatform"
    ]
 }
```

So when recursive build is executed then the order of execution will be LMGP -> LMBC -> Feature
Package(s).

## LMBC Output

LMBC produces a JAR file along with supported plugins. This outputs will be available directly
through wire-daemon. No extra integration steps are needed for this.

## IDEA Setup

💡 To open all the workspace projects in the same IDEA, perform `includeBuild` in Last Mile
Package

`settings.gradle`

```
...

file('..').listFiles().findAll {
    it.directory && file(it.canonicalPath + "/brazil.ion").exists()
}.each {
    includeBuild "../${it.name}"
}
```

## Guard Rails enabled through LMBC

1. **KtLint** is added and is enforced for all Kotlin files.

`./gradlew ktlintCheck` : Performs lint checks on modified and new files.

`./gradlew ktlintFormat` : Automatically fixes linting issues. *Mostly, this fixes linting issues
but
there can be some rules that cannot be auto-corrected and will need manual intervention.*

2. **Detekt** is added for new/modified Kotlin files but "not" enforced 
(*Detekt will be enforced from 11/14/2023 after P2 release*). 
RuleSet for Detekt is configured at [LMBC-Detekt-Ruleset](https://code.amazon.com/packages/LastMileBuildComponent/blobs/mainline/--/src/main/resources/configuration/detekt/detekt.yml)

`./gradlew detekt`: Performs code smell check on modified and new files.

💡 To run detekt on all files add 

`build.gradle`
```
...

lmbc {
    codeQuality {
        ...
        detektSource = "all" [Note: other supported option is "modified"]
    }
}
```
In addition, you can commit build.gradle to always run on all files in the package. Use same command `./gradlew detekt` to run detekt.


3. **PreCommit Hook** will be executed during git-commit. The only umbrella tasks that runs at this
   time is `preCommitCodeChecks`. This task will run all the supported analyzers before code is
   committed.

`./gradlew build` : PreCommit Hook will be automatically copied to each workspace(.git/hooks)
during `build`
task. Gradle caching mechanism only re-copies if the file content is changed.

`./gradlew lmbcSetup` : Manual command to copy pre-commit hooks to package workspace (.git/hooks).

`./gradlew preCommitCodeChecks` : Manually triggers preCommitCodeChecks.

⚙️ *In Progress:*

*For Java Packages: CheckStyle, Spotbugs.*

*For Android Packages: CheckStyle, Spotbugs.*

### Extension Properties

Some plugins need configurations to customize plugin behavior. Here are the list of configurations
needed for plugins:

#### Configuration for Library/Application Plugins

Every library/application need to provide `group` and `version` data about the artifact.

`build.gradle`

```
plugins {
    id 'com.amazon.lastmile.android.lib'
}

group = 'com.amazon.lastmile.example'
version = '1.0.0'
```

#### Configuration for Android Application Plugins

Android application need configurations for creating APK. In addition to `group` and `version`,
Android Application Plugin will also need to share `android` configuration.

```
plugins {
    id 'com.amazon.lastmile.android.app'
}

group = 'com.amazon.lastmile.example'
version = '1.0.0'

android {
    defaultConfig {
        applicationId 'com.amazon.lastmile.example'
        versionCode 1
        versionName '1.0'
    }
}

```

### Configuration for feature specific behavior

LMBC pushes for consistent behavior across all Last Mile packages. LMBC do open this configuration
unless there is an experimental scope for featutes.

For example:
Ktlint uprade from 0.31.0 to 1.0.0 will be supported by this configuration until experimental period
expires.

LMBC provides this configuration with extension property `lmbc` that contains inner object
factories.

```
lmbc {
	codeQuality {
		ktlintVersion = "1.0.0"
	}
	...
}
```

Inner object factory will be expanded based on new experimental features.

## Recommendations

### Common logic goes into plugin

Build scripts that is re-used by multiple packages need to part of a separate convention plugin.

### Less flexibility on configurations

LMBC thrives for providing consistent build behavior across all Last Mile Feature Packages. LMBC
will define most of the mandatory configurations for modules. @see **Extension Properties** section.

### Controlled dependency versions

LMGP controls all the dependency versions.
Read [LMGP documentation](https://code.amazon.com/packages/LastMilePeruPlatform/logs/heads/mainline)
about dependency management.

### Standard extension properties

Gradle outputs are not developer friendly to provide the suggestions in IDEA when plugins are
applied. So, aligned with standard configurations wherever applicable. Example:

```
android {
    defaultConfig {
        applicationId 'com.amazon.lastmile.example'
        versionCode 1
        versionName '1.0'
    }
}
```

## References

[Technical Design](https://wiki.labcollab.net/confluence/display/DTO/Last+Mile+Build+System+Design)

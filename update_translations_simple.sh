#!/bin/bash

# Simple Translation Update Script (No Panther)
# This script only converts existing puff.json files to XML
# Use this when panther translate has issues or when you have existing translation files
# Usage: ./update_translations_simple.sh

set -e  # Exit on any error

echo "🔄 Simple Translation Update Script"
echo "=================================="
echo "ℹ️  This script converts existing puff.json files to XML without running panther translate"

# Check if we're in the right directory
if [ ! -f "build.gradle" ] || [ ! -d "src/main/sourcemessages" ]; then
    echo "❌ Error: This script must be run from the MapsEngagementAndroidSDK root directory"
    echo "   Current directory: $(pwd)"
    echo "   Expected files: build.gradle, src/main/sourcemessages/"
    exit 1
fi

# Check for existing translation files
echo ""
echo "📁 Checking for existing translation files..."
echo "--------------------------------------------"

TRANSLATION_DIRS=(
    "src/main/sourcemessages/native/translations"
    "src/main/sourcemessages/native"
)

FOUND_FILES=false

for dir in "${TRANSLATION_DIRS[@]}"; do
    if [ -d "$dir" ]; then
        file_count=$(find "$dir" -name "MapsFeedback*.puff.json" -not -name "MapsFeedback.puff.json" 2>/dev/null | wc -l)
        if [ "$file_count" -gt 0 ]; then
            echo "✅ Found $file_count translation files in $dir"
            find "$dir" -name "MapsFeedback*.puff.json" -not -name "MapsFeedback.puff.json" 2>/dev/null | head -3
            if [ "$file_count" -gt 3 ]; then
                echo "   ... and $((file_count - 3)) more files"
            fi
            FOUND_FILES=true
        fi
    fi
done

if [ "$FOUND_FILES" = false ]; then
    echo "❌ No translation files found"
    echo "   Expected files like: MapsFeedback-de-DE.puff.json or MapsFeedback_de_DE.puff.json"
    echo "   Please run panther translate first or ensure translation files exist"
    exit 1
fi

# Convert puff.json to XML
echo ""
echo "🔄 Converting puff.json files to XML..."
echo "--------------------------------------"

if [ ! -f "convert_puff_to_xml.py" ]; then
    echo "❌ Error: convert_puff_to_xml.py not found in project root"
    exit 1
fi

# Run the conversion script
if python3 convert_puff_to_xml.py; then
    echo ""
    echo "🎉 Translation update completed successfully!"
    echo ""
    echo "📊 Summary:"
    echo "  - Used existing translation files: ✅"
    echo "  - XML conversion: ✅"
    echo "  - Updated strings.xml files in multiple locales"
    echo ""
    echo "💡 Next steps:"
    echo "  - Review the changes: git diff"
    echo "  - Test your app with the new translations"
    echo "  - Commit the changes: git add . && git commit -m 'Update translations'"
    echo ""
    echo "ℹ️  To get fresh translations from Panther, fix the panther environment and run:"
    echo "   ./update_translations.sh"
else
    echo "❌ XML conversion failed"
    exit 1
fi

#!/usr/bin/env python3
import os
from os import listdir
from os.path import isfile, join
import re
import json
import sys

#This script will rename all Puff-J files within the translations folder to have the proper locale extension needed for importing resources into the Rabbit app.
#This script should be run after translations are received from Panther. 
#When translations are received from Panther, some translations puff-j files will have locales with (xx-YY) format (e.g. strings-es-ES.puff.json).
#Some of these locales need to be converted from (xx-YY) to (xx) due to Android localization formatting.  
#For example, resources in the 'ES' locale are named with values-es in Android. 
#This script will rename puff-j files in the translations folder to contain the proper locale extension needed for Anrdoid (e.g. strings-es-ES.puff.json -> strings-es.puff.json).
#After running this script, the PantherAndroidGradlePlugin can be run to copy puff-j reosurces to Android resource folders. 

configFile = open('../pantherConfig.json')
configJson = json.load(configFile)
SOURCE_LOCALES = configJson["SOURCE_LOCALES"]

totalFilesRenamed = 0

#Generate map which maps Panther puff-j locale extensions to the required Android locale 
pantherLocaleExtensionMap = {}
targetAndroidLocaleExtensionSet = set()
for locale in SOURCE_LOCALES: 
	if "locale" not in locale:
		continue
	if "folder" not in locale:
		continue
	pantherLocaleExtensionMap[locale["locale"].replace('_', '-')] = locale["folder"]
	targetAndroidLocaleExtensionSet.add(locale["folder"])

puffjPath = "../../sourcemessages/native"
homeDir = os.getcwd()
HOME_DIRECTORY_ABSOLUTE = os.path.abspath(os.getcwd())
os.chdir(puffjPath)
translationsPath = os.path.abspath(os.getcwd())

missingLocales = set()

onlyfiles = [f for f in listdir(translationsPath) if isfile(join(translationsPath, f))]
for f in onlyfiles:
	if '-' not in f:
		continue
	puffjLocaleSplit = f.split('-', 1)

	#Fetch the name of the file (before the locale extension - e.g. Strings)
	preExtension = puffjLocaleSplit[0]

	#Fetch the locale extension (e.g. fr-rCA.puff.json)
	puffjExtension = puffjLocaleSplit[1]
	if '.puff.json' not in puffjExtension:
		continue

	#Fetch the locale (e.g. fr-rCA)
	sourceLocale = puffjExtension.split('.puff.json')[0]

	#Fetch the proper Android locale that maps to the Panther locale from the Panther config file (i.e. 'ja-JP' should be 'ja')
	if sourceLocale not in pantherLocaleExtensionMap:
		if sourceLocale in targetAndroidLocaleExtensionSet:
			#File already has the proper Android locale format and does not need to be renamed.
			continue
		missingLocales.add(sourceLocale)
		continue

	#Skip locales that are already properly formatted (e.g. xx-YY).  These locales will already work properly with the PantherAndroidGradlePlugin.
	if '-' in pantherLocaleExtensionMap[sourceLocale]:
		continue

	properPuffjPath = preExtension + '-' + pantherLocaleExtensionMap[sourceLocale] + ".puff.json"
	currentFilePath = join(translationsPath, f)
	newFilePath = join(translationsPath, properPuffjPath)

	if currentFilePath == newFilePath:
		continue
	
	#Rename the file to include the proper Android locale prior to running the PantherAndroidGradlePlugin
	#This will ensure that Panther Puff-J files get copied to the proper folder.  
	os.rename(currentFilePath, newFilePath)
	totalFilesRenamed += 1

if len(missingLocales) > 0:
	missingLocalesList = ""
	for idx, loc in enumerate(missingLocales):
		if idx == 0:
			missingLocalesList += loc
		else:
			missingLocalesList += ", "
			missingLocalesList += loc 
	print("WARNING: Extensions were not found in the pantherConfig.json file in the panther_scripts folder.  Please add the following extensions to the SOURCE_LOCALES section of the config file if they are supported by the app: " + missingLocalesList)

if totalFilesRenamed == 0:
	print("SUCCESS - All puff-j files already have the proper Android locale format!")
else:
	print("SUCCESS - Renamed " + str(totalFilesRenamed) + " puff-j files to Android locale format!")

#!/usr/bin/env python3

import json
import os
import xml.etree.ElementTree as ET
from xml.dom import minidom
import re

def create_locale_mapping():
    """Create mapping from puff.json locale codes to Android values folder names"""
    return {
        'ar_AE': 'values-ar-rAE',
        'en_AE': 'values-en-rAE',
        'en_IN': 'values-en-rIN',
        'de_DE': 'values-de-rDE',
        'de': 'values-de',  # German (simplified)
        'en_GB': 'values-en-rGB',
        'en_SG': 'values-en-rSG',
        'es_ES': 'values-es-rES',
        'es': 'values-es',  # Spanish (simplified)
        'es_MX': 'values-es-rMX',
        'fr_FR': 'values-fr-rFR',
        'fr': 'values-fr-rFR',  # French (map to fr-rFR)
        'hi_IN': 'values-hi-rIN',
        'kn_IN': 'values-kn-rIN',
        'ml_IN': 'values-ml-rIN',
        'ta_IN': 'values-ta-rIN',
        'te_IN': 'values-te-rIN',
        'it_IT': 'values-it-rIT',
        'it': 'values-it-rIT',  # Italian (map to it-rIT)
        'zh_CN': 'values-zh-rCN',
        'en_CA': 'values-en-rCA',
        'ja_JP': 'values-ja',
        'ro_RO': 'values-ro',
        'ro': 'values-ro',  # Romanian (simplified)
        'pl_PL': 'values-pl',
        'pl': 'values-pl',  # Polish (simplified)
        'bg_BG': 'values-bg',
        'en_AU': 'values-en-rAU',
        'fr_CA': 'values-fr-rCA',
        'en_IE': 'values-en-rIE',
        'nl_NL': 'values-nl-rNL',
        'pt_BR': 'values-pt-rBR',
        'tr_TR': 'values-tr-rTR',
        'pt_PT': 'values-pt-rPT',
        'ru_RU': 'values-ru',
        'vi_VN': 'values-vi',
        'vi': 'values-vi',  # Vietnamese (simplified)
        'en_US': 'values'  # Default English
    }

def escape_xml_string(text):
    """Escape special characters for XML"""
    if not text:
        return text
    
    # Replace special characters
    text = text.replace('&', '&amp;')
    text = text.replace('<', '&lt;')
    text = text.replace('>', '&gt;')
    text = text.replace('"', '&quot;')
    text = text.replace("'", "\\'")
    
    return text

def load_puff_json(file_path):
    """Load and parse a puff.json file"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"Error loading {file_path}: {e}")
        return None

def update_strings_xml(values_dir, new_strings):
    """Update or create strings.xml file in the given values directory"""
    strings_xml_path = os.path.join(values_dir, 'strings.xml')
    
    # Create directory if it doesn't exist
    os.makedirs(values_dir, exist_ok=True)
    
    # Load existing XML or create new one
    if os.path.exists(strings_xml_path):
        try:
            tree = ET.parse(strings_xml_path)
            root = tree.getroot()
        except ET.ParseError as e:
            print(f"Error parsing {strings_xml_path}: {e}")
            return False
    else:
        # Create new XML structure
        root = ET.Element('resources')
        tree = ET.ElementTree(root)
    
    # Update or add strings
    updated_count = 0
    added_count = 0
    
    for string_name, string_value in new_strings.items():
        # Find existing string element
        existing_element = root.find(f".//string[@name='{string_name}']")
        
        if existing_element is not None:
            # Update existing string
            existing_element.text = escape_xml_string(string_value)
            updated_count += 1
        else:
            # Add new string element
            string_element = ET.SubElement(root, 'string')
            string_element.set('name', string_name)
            string_element.text = escape_xml_string(string_value)
            added_count += 1
    
    # Write back to file with proper formatting
    try:
        # Convert to string with proper formatting
        rough_string = ET.tostring(root, encoding='unicode')
        reparsed = minidom.parseString(rough_string)
        pretty_xml = reparsed.toprettyxml(indent="    ")
        
        # Remove extra blank lines and fix formatting
        lines = pretty_xml.split('\n')
        lines = [line for line in lines if line.strip()]
        
        # Write to file
        with open(strings_xml_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(lines))
        
        print(f"✅ Updated {values_dir}/strings.xml: {updated_count} updated, {added_count} added")
        return True
        
    except Exception as e:
        print(f"❌ Error writing {strings_xml_path}: {e}")
        return False

def process_puff_files():
    """Process all puff.json files and update corresponding XML files"""
    puff_dir = "src/main/sourcemessages/native"
    res_dir = "src/main/res"
    locale_mapping = create_locale_mapping()
    
    print("=== Converting Puff.json files to XML string resources ===")
    
    # Process all puff.json files
    for filename in os.listdir(puff_dir):
        if not filename.endswith('.puff.json'):
            continue
            
        # Skip the source file (no locale suffix)
        if filename == 'MapsFeedback.puff.json':
            continue
            
        # Extract locale from filename (e.g., MapsFeedback_de_DE.puff.json -> de_DE)
        match = re.match(r'MapsFeedback_(.+)\.puff\.json', filename)
        if not match:
            print(f"⚠️  Skipping {filename}: couldn't extract locale")
            continue
            
        locale = match.group(1)
        
        # Map to Android values folder
        if locale not in locale_mapping:
            print(f"⚠️  Skipping {filename}: no mapping for locale '{locale}'")
            continue
            
        values_folder = locale_mapping[locale]
        values_dir = os.path.join(res_dir, values_folder)
        
        # Load puff.json file
        puff_path = os.path.join(puff_dir, filename)
        puff_data = load_puff_json(puff_path)
        
        if not puff_data or 'resources' not in puff_data:
            print(f"⚠️  Skipping {filename}: invalid format")
            continue
            
        # Extract strings from puff.json
        new_strings = {}
        for key, value in puff_data['resources'].items():
            if isinstance(value, dict) and 'value' in value:
                new_strings[key] = value['value']
        
        if not new_strings:
            print(f"⚠️  Skipping {filename}: no strings found")
            continue
            
        # Update XML file
        print(f"📝 Processing {filename} -> {values_folder}/strings.xml")
        update_strings_xml(values_dir, new_strings)

if __name__ == "__main__":
    process_puff_files()
    print("\n🎉 Conversion completed!")

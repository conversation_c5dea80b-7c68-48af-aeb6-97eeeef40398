#!/usr/bin/env python3

import json
import os
import re

def create_locale_mapping():
    """Create mapping from puff.json locale codes to Android values folder names"""
    return {
        'ar_AE': 'values-ar-rAE',
        'en_AE': 'values-en-rAE',
        'en_IN': 'values-en-rIN',
        'de_DE': 'values-de-rDE',
        'de': 'values-de',  # German (simplified)
        'en_GB': 'values-en-rGB',
        'en_SG': 'values-en-rSG',
        'es_ES': 'values-es-rES',
        'es': 'values-es',  # Spanish (simplified)
        'es_MX': 'values-es-rMX',
        'fr_FR': 'values-fr-rFR',
        'fr': 'values-fr-rFR',  # French (map to fr-rFR)
        'hi_IN': 'values-hi-rIN',
        'kn_IN': 'values-kn-rIN',
        'ml_IN': 'values-ml-rIN',
        'ta_IN': 'values-ta-rIN',
        'te_IN': 'values-te-rIN',
        'it_IT': 'values-it-rIT',
        'it': 'values-it-rIT',  # Italian (map to it-rIT)
        'zh_CN': 'values-zh-rCN',
        'en_CA': 'values-en-rCA',
        'ja_JP': 'values-ja',
        'ro_RO': 'values-ro',
        'ro': 'values-ro',  # Romanian (simplified)
        'pl_PL': 'values-pl',
        'pl': 'values-pl',  # Polish (simplified)
        'bg_BG': 'values-bg',
        'en_AU': 'values-en-rAU',
        'fr_CA': 'values-fr-rCA',
        'en_IE': 'values-en-rIE',
        'nl_NL': 'values-nl-rNL',
        'pt_BR': 'values-pt-rBR',
        'tr_TR': 'values-tr-rTR',
        'pt_PT': 'values-pt-rPT',
        'ru_RU': 'values-ru',
        'vi_VN': 'values-vi',
        'vi': 'values-vi',  # Vietnamese (simplified)
        'en_US': 'values'  # Default English
    }

def escape_xml_string(text):
    """Escape special characters for XML"""
    if not text:
        return text

    # Replace special characters in the correct order
    text = text.replace('&', '&amp;')  # Must be first
    text = text.replace('<', '&lt;')
    text = text.replace('>', '&gt;')
    text = text.replace('"', '&quot;')
    text = text.replace("'", "\\'")

    return text

def load_puff_json(file_path):
    """Load and parse a puff.json file"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"Error loading {file_path}: {e}")
        return None

def update_strings_xml(values_dir, new_strings):
    """Update or create strings.xml file in the given values directory, preserving original formatting"""
    strings_xml_path = os.path.join(values_dir, 'strings.xml')

    # Create directory if it doesn't exist
    os.makedirs(values_dir, exist_ok=True)

    updated_count = 0
    added_count = 0

    if os.path.exists(strings_xml_path):
        # Read the existing file
        try:
            with open(strings_xml_path, 'r', encoding='utf-8') as f:
                content = f.read()
        except Exception as e:
            print(f"❌ Error reading {strings_xml_path}: {e}")
            return False

        # Process each string to add/update
        for string_name, string_value in new_strings.items():
            escaped_value = escape_xml_string(string_value)

            # Pattern to find existing string with this name
            pattern = rf'<string\s+name="{re.escape(string_name)}"[^>]*>.*?</string>'

            if re.search(pattern, content, re.DOTALL):
                # Update existing string - preserve the exact formatting style
                def replace_string_content(match):
                    # Extract the opening tag and preserve its formatting
                    opening_tag = re.match(r'<string[^>]*>', match.group(0)).group(0)
                    return f'{opening_tag}{escaped_value}</string>'

                content = re.sub(pattern, replace_string_content, content, flags=re.DOTALL)
                updated_count += 1
            else:
                # Add new string before the closing </resources> tag
                # Find the last string element to match its indentation
                last_string_match = None
                for match in re.finditer(r'^(\s*)<string\s+name="[^"]*"[^>]*>.*?</string>', content, re.MULTILINE | re.DOTALL):
                    last_string_match = match

                if last_string_match:
                    # Use the same indentation as the last string
                    indent = last_string_match.group(1)
                else:
                    # Default indentation if no strings found
                    indent = "    "

                new_string_line = f'{indent}<string name="{string_name}">{escaped_value}</string>'

                # Insert before the closing </resources> tag
                resources_end_pattern = r'(\s*)</resources>'
                replacement = f'\n{new_string_line}\n\\1</resources>'
                content = re.sub(resources_end_pattern, replacement, content)
                added_count += 1

        # Write back the modified content
        try:
            with open(strings_xml_path, 'w', encoding='utf-8') as f:
                f.write(content)
        except Exception as e:
            print(f"❌ Error writing {strings_xml_path}: {e}")
            return False

    else:
        # Create new XML file
        content = '<?xml version="1.0" encoding="utf-8"?>\n<resources>\n'
        for string_name, string_value in new_strings.items():
            escaped_value = escape_xml_string(string_value)
            content += f'    <string name="{string_name}">{escaped_value}</string>\n'
            added_count += 1
        content += '</resources>\n'

        try:
            with open(strings_xml_path, 'w', encoding='utf-8') as f:
                f.write(content)
        except Exception as e:
            print(f"❌ Error creating {strings_xml_path}: {e}")
            return False

    print(f"✅ Updated {values_dir}/strings.xml: {updated_count} updated, {added_count} added")
    return True

def process_puff_files():
    """Process all puff.json files and update corresponding XML files"""
    puff_dir = "src/main/sourcemessages/native"
    translations_dir = "src/main/sourcemessages/native/translations"
    res_dir = "src/main/res"
    locale_mapping = create_locale_mapping()

    print("=== Converting Puff.json files to XML string resources ===")

    # Check both directories for puff.json files
    search_dirs = [puff_dir, translations_dir]

    for search_dir in search_dirs:
        if not os.path.exists(search_dir):
            continue

        # Process all puff.json files in this directory
        for filename in os.listdir(search_dir):
            if not filename.endswith('.puff.json'):
                continue

            # Skip the source file (no locale suffix)
            if filename == 'MapsFeedback.puff.json':
                continue

            # Extract locale from filename - handle both formats:
            # MapsFeedback_de_DE.puff.json -> de_DE (underscore format)
            # MapsFeedback-de-DE.puff.json -> de_DE (hyphen format)
            match = re.match(r'MapsFeedback[-_](.+)\.puff\.json', filename)
            if not match:
                print(f"⚠️  Skipping {filename}: couldn't extract locale")
                continue

            locale = match.group(1).replace('-', '_')  # Convert hyphens to underscores
        
            # Map to Android values folder
            if locale not in locale_mapping:
                print(f"⚠️  Skipping {filename}: no mapping for locale '{locale}'")
                continue

            values_folder = locale_mapping[locale]
            values_dir = os.path.join(res_dir, values_folder)

            # Load puff.json file
            puff_path = os.path.join(search_dir, filename)
            puff_data = load_puff_json(puff_path)

            if not puff_data or 'resources' not in puff_data:
                print(f"⚠️  Skipping {filename}: invalid format")
                continue

            # Extract strings from puff.json
            new_strings = {}
            for key, value in puff_data['resources'].items():
                if isinstance(value, dict) and 'value' in value:
                    new_strings[key] = value['value']

            if not new_strings:
                print(f"⚠️  Skipping {filename}: no strings found")
                continue

            # Update XML file
            print(f"📝 Processing {filename} -> {values_folder}/strings.xml")
            update_strings_xml(values_dir, new_strings)

if __name__ == "__main__":
    process_puff_files()
    print("\n🎉 Conversion completed!")

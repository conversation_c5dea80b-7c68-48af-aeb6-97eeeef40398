# Panther Translation Automation for MapsEngagementAndroidSDK

This document describes the automated translation workflow that converts Panther puff.json files to Android XML string resources.

## Overview

The translation system provides manual tools to:
1. Pull translations from Panther (when available)
2. Convert puff.json files to XML string resources
3. Update the appropriate `values-*/strings.xml` files for each locale
4. Preserve original XML formatting and encoding
5. Run from the project root directory without navigating to subdirectories

## Files Added/Modified

### New Files
- `convert_puff_to_xml.py` - Python script that converts puff.json to XML
- `update_translations.sh` - Complete workflow script (panther translate + XML conversion)
- `update_translations_simple.sh` - Simple script that uses existing files (no panther)
- `convert_translations.sh` - XML conversion only script
- `README_TRANSLATIONS.md` - This documentation

### Modified Files
- `build.gradle` - Added translation tasks that use the shell scripts
- `.gitignore` - Added translations directory to ignore list

### Generated Files (Ignored by Git)
- `src/main/sourcemessages/native/translations/*.puff.json` - Translation files from Panther

## How It Works

### 1. Panther Translation Files

Source files are located in:
- `src/main/sourcemessages/native/MapsFeedback.puff.json` - Source English strings
- `src/main/sourcemessages/native/translations/MapsFeedback-*.puff.json` - Translated strings (generated by panther translate)

The script automatically handles both naming formats:
- Hyphen format: `MapsFeedback-de-DE.puff.json`
- Underscore format: `MapsFeedback_de_DE.puff.json`

### 2. Locale Mapping

The script maps Panther locale codes to Android values folders:
- `de_DE` → `values-de-rDE/strings.xml`
- `es_MX` → `values-es-rMX/strings.xml`
- `en_GB` → `values-en-rGB/strings.xml`
- `en_US` → `values/strings.xml` (default)
- etc.

### 3. XML Integration

For each locale, the script:
- Loads the existing `strings.xml` file
- Updates existing string keys with new translations
- Adds new string keys at the end of the file
- **Preserves original XML declaration**: `<?xml version="1.0" encoding="utf-8"?>`
- **Preserves all comments and formatting**
- Uses text-based manipulation to avoid reformatting

## Usage

### Recommended: Complete Workflow (Panther + XML)

**Shell Script (Recommended):**
```bash
# Run panther translate + XML conversion from project root
./update_translations.sh
```

**Gradle Task:**
```bash
# Same as above, but via Gradle
./gradlew updateTranslations
```

### Simple Workflow (Existing Files Only)

**When Panther Has Issues:**
```bash
# Use existing translation files without running panther translate
./update_translations_simple.sh
```

This script is useful when:
- Panther translate fails due to workspace/environment issues
- You want to use existing translation files
- Brazil/Peru workspace configuration problems occur

### XML Conversion Only

**Shell Script:**
```bash
# Convert existing puff.json files to XML only
./convert_translations.sh
```

**Gradle Task:**
```bash
# Same as above, but via Gradle
./gradlew convertPuffToXml
```

### Individual Tasks
```bash
# Just run panther translate only
./gradlew pantherTranslate

# Regular build (no automatic translation updates)
./gradlew build
```

## Key Features

### ✅ **Easy to Use**
- **Run from project root**: No need to navigate to subdirectories
- **Single command**: `./update_translations.sh` does everything
- **Smart error handling**: Gracefully handles missing panther command
- **Clear output**: Shows progress and results for each step

### ✅ **Preserves Formatting**
- **XML declaration**: Keeps `<?xml version="1.0" encoding="utf-8"?>`
- **Comments**: Preserves all existing comments
- **Indentation**: Maintains original formatting
- **Text-based**: Uses regex instead of XML parsing to avoid reformatting

### ✅ **Flexible Workflow**
- **Complete workflow**: `./update_translations.sh` (panther + conversion)
- **Conversion only**: `./convert_translations.sh` (existing files only)
- **Gradle integration**: Works with both shell scripts and Gradle tasks
- **Manual execution**: Run only when needed, no automatic build integration

## Workflow Steps

1. **Panther Translate** (via `update_translations.sh`)
   - Changes to `src/main/sourcemessages/native/` directory
   - Runs `panther translate` to fetch latest translations
   - Gracefully handles cases where panther command is not available
   - Updates all `MapsFeedback-*.puff.json` files in the translations directory

2. **Convert to XML** (via both scripts)
   - Searches both `src/main/sourcemessages/native/` and `src/main/sourcemessages/native/translations/`
   - Processes all translated puff.json files (handles both hyphen and underscore formats)
   - Maps locale codes to Android values folders
   - Updates corresponding `strings.xml` files using text-based manipulation
   - Preserves original XML formatting, encoding, and comments

3. **Manual Execution**
   - `./gradlew updateTranslations` runs the complete workflow
   - `./gradlew convertPuffToXml` runs conversion only
   - `./gradlew build` runs normally without translation updates
   - All tasks use the shell scripts for consistency
   - Translations are updated only when explicitly requested

## When to Run Translation Updates

### After Adding New Strings
1. Add new strings to `src/main/sourcemessages/native/MapsFeedback.puff.json`
2. Run `./update_translations.sh` to pull translations and update XML files
3. Review the changes and commit them

### After Translation Updates
1. When translators update strings in Panther
2. Run `./update_translations.sh` to pull latest translations
3. Review and commit the updated strings.xml files

### Before Releases
1. Run `./update_translations.sh` to ensure all translations are current
2. Test the app with updated translations
3. Include translation updates in the release

## Adding New Strings

1. Add new strings to `src/main/sourcemessages/native/MapsFeedback.puff.json`
2. Run `./update_translations.sh` to get translations and convert to XML
3. New strings will be added to all locale-specific `strings.xml` files
4. Review and commit the changes

## Supported Locales

The system supports all locales with existing `values-*` folders:
- Arabic (UAE): `values-ar-rAE`
- German: `values-de`
- Spanish: `values-es`, `values-es-rMX`
- French: `values-fr-rFR`, `values-fr-rCA`
- Italian: `values-it-rIT`
- And many more...

## Troubleshooting

### Panther Command Not Found
If `panther translate` fails, the system will use existing puff.json files and continue with the conversion.

### Panther Workspace/Environment Issues
If you see errors like:
- `'brazil-bootstrap' command is not supported for 'peru' version set type`
- `Error rebuilding BrazilPath environment: Failed to bootstrap environment`
- `filename should not contain hyphen("-")`

**Solution**: Use the simple script instead:
```bash
./update_translations_simple.sh
```

This bypasses panther translate and uses existing translation files.

### Python Not Found
Ensure Python 3 is installed and available as `python3` in your PATH.

### Missing Locale Mapping
If a puff.json file is skipped, add the locale mapping in `convert_puff_to_xml.py`.

## Example Output

```
🌍 Pulling translations from Panther...
✅ Panther translate step completed
🔄 Converting puff.json files to XML string resources...
📝 Processing MapsFeedback_de.puff.json -> values-de/strings.xml
✅ Updated src/main/res/values-de/strings.xml: 1 updated, 0 added
📝 Processing MapsFeedback_es_MX.puff.json -> values-es-rMX/strings.xml
✅ Updated src/main/res/values-es-rMX/strings.xml: 0 updated, 1 added
🎉 Conversion completed!
🎉 All translations updated successfully!
```

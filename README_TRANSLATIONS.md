# Panther Translation Automation for MapsEngagementAndroidSDK

This document describes the automated translation workflow that converts Panther puff.json files to Android XML string resources.

## Overview

The translation system automatically:
1. Pulls translations from Panther (when available)
2. Converts puff.json files to XML string resources
3. Updates the appropriate `values-*/strings.xml` files for each locale
4. Integrates with the Gradle build process

## Files Added/Modified

### New Files
- `convert_puff_to_xml.py` - Python script that converts puff.json to XML
- `run_panther_translate.sh` - Shell script to run panther translate
- `README_TRANSLATIONS.md` - This documentation

### Modified Files
- `build.gradle` - Added translation tasks
- `local.properties` - Added auto_pull_translations setting

## How It Works

### 1. Panther Translation Files
Source files are located in `src/main/sourcemessages/native/`:
- `MapsFeedback.puff.json` - Source English strings
- `MapsFeedback_*.puff.json` - Translated strings for each locale

### 2. Locale Mapping
The script maps Panther locale codes to Android values folders:
- `de` → `values-de/strings.xml`
- `es_MX` → `values-es-rMX/strings.xml`
- `en_GB` → `values-en-rGB/strings.xml`
- etc.

### 3. XML Integration
For each locale, the script:
- Loads the existing `strings.xml` file
- Updates existing string keys with new translations
- Adds new string keys at the end of the file
- Preserves existing formatting and comments

## Usage

### Manual Translation Update
```bash
# Update translations manually
./gradlew updateTranslations
```

### Automatic with Build
```bash
# Translations are automatically updated during build
./gradlew build
```

### Individual Tasks
```bash
# Just run panther translate (if available)
./gradlew pantherTranslate

# Just convert existing puff.json to XML
./gradlew convertPuffToXml
```

## Workflow Steps

1. **Panther Translate** (`pantherTranslate` task)
   - Runs `panther translate` in `src/main/sourcemessages/native/`
   - Gracefully handles cases where panther command is not available
   - Updates all `MapsFeedback_*.puff.json` files

2. **Convert to XML** (`convertPuffToXml` task)
   - Processes all translated puff.json files
   - Maps locale codes to Android values folders
   - Updates corresponding `strings.xml` files
   - Preserves existing strings and adds new ones

3. **Build Integration** (`updateTranslations` task)
   - Automatically runs before the main build
   - Ensures translations are always up-to-date

## Adding New Strings

1. Add new strings to `src/main/sourcemessages/native/MapsFeedback.puff.json`
2. Run `panther translate` to get translations (or manually if panther is not available)
3. Run `./gradlew updateTranslations` to convert to XML
4. New strings will be added to all locale-specific `strings.xml` files

## Supported Locales

The system supports all locales with existing `values-*` folders:
- Arabic (UAE): `values-ar-rAE`
- German: `values-de`
- Spanish: `values-es`, `values-es-rMX`
- French: `values-fr-rFR`, `values-fr-rCA`
- Italian: `values-it-rIT`
- And many more...

## Troubleshooting

### Panther Command Not Found
If `panther translate` fails, the system will use existing puff.json files and continue with the conversion.

### Python Not Found
Ensure Python 3 is installed and available as `python3` in your PATH.

### Missing Locale Mapping
If a puff.json file is skipped, add the locale mapping in `convert_puff_to_xml.py`.

## Example Output

```
🌍 Pulling translations from Panther...
✅ Panther translate step completed
🔄 Converting puff.json files to XML string resources...
📝 Processing MapsFeedback_de.puff.json -> values-de/strings.xml
✅ Updated src/main/res/values-de/strings.xml: 1 updated, 0 added
📝 Processing MapsFeedback_es_MX.puff.json -> values-es-rMX/strings.xml
✅ Updated src/main/res/values-es-rMX/strings.xml: 0 updated, 1 added
🎉 Conversion completed!
🎉 All translations updated successfully!
```

plugins {
    id("com.amazon.lastmile.android.lib") version "1.0+"
}

lmbc {
    codeQuality {
        ktlintVersion("1.0.0")
        setDetektSource("all")
    }
}

dependencies {
    // AndroidX
    implementation("androidx.coordinatorlayout:coordinatorlayout")
    implementation("androidx.lifecycle:lifecycle-viewmodel-ktx")
    implementation("androidx.recyclerview:recyclerview")

    // Kotlin
    implementation("org.jetbrains.kotlin:kotlin-reflect")
    implementation("org.jetbrains.kotlin:kotlin-stdlib")
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-core")
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-android")

    // 3P Libs
    implementation("com.google.android.material:material")
    implementation("com.google.code.findbugs:jsr305")
    implementation("com.google.code.gson:gson")
    implementation("com.squareup.dagger:dagger")
    implementation("io.reactivex.rxjava3:rxandroid")
    implementation("io.reactivex.rxjava3:rxjava")
    implementation("net.danlew:android.joda")
    implementation("net.cachapa.expandablelayout:expandablelayout")

    // Majix
    implementation("com.amazon.lastmile:MajixAndroidPlatform")
    implementation("com.amazon.lastmile:Astrogator")
    implementation("com.amazon.lastmile:GranTorino")
    implementation("com.amazon.lastmile:ElCaminoLib")
    implementation("com.amazon.lastmile:LocationAndroidModel")
    implementation("com.amazon.lastmile:LocationAndroidSDK")
    implementation("com.amazon.lastmile:VisualMapsAndroidModel")
    implementation("com.amazon.lastmile:VisualMapsAndroidSDK")
    implementation("com.amazon.lastmile:MapsEngagementAndroidModel")
    implementation("com.amazon.lastmile:RDSCustomComponentsAndroid")
    implementation("com.amazon.lastmile:RDSMeridianAndroid")

    // Testing
    testImplementation("org.mockito:mockito-core")
    testImplementation("org.jetbrains.kotlinx:kotlinx-coroutines-test")
    testImplementation("net.bytebuddy:byte-buddy")
    testImplementation("org.robolectric:robolectric")
    testImplementation("androidx.test:core")
    testImplementation("io.mockk:mockk:1.14.2")
    testImplementation("org.assertj:assertj-core")
}

android.namespace = "com.amazon.maps.engagement.sdk"
group = "com.amazon.lastmile"
version = "1.0"


// Custom task to convert puff.json files to XML string resources
task convertPuffToXml(type: Exec) {
    commandLine 'python3', 'convert_puff_to_xml.py'
    doFirst {
        println "🔄 Converting puff.json files to XML string resources..."
    }
    doLast {
        println "✅ XML conversion completed"
    }
}

// Task to update all translations
task updateTranslations {
    dependsOn convertPuffToXml
    doLast {
        println "🎉 All translations updated successfully!"
    }
}

// Make the build task depend on translation updates
build.dependsOn updateTranslations

#!/bin/bash

echo "=== Running Panther Translate ==="

# Change to the directory where panther.cfg is located
cd src/main/sourcemessages/native

# Run panther translate
panther translate

if [ $? -eq 0 ]; then
    echo "✅ Panther translate completed successfully"
    cd ../../../..
    echo "📁 Current directory: $(pwd)"
    echo "📄 Generated translation files:"
    find src/main/sourcemessages/native -name "*.puff.json" -not -name "MapsFeedback.puff.json" | sort
else
    echo "❌ Panther translate failed"
    exit 1
fi

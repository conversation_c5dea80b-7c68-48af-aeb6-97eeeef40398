#!/bin/bash

# Panther Translation Update Script
# This script runs panther translate and updates XML string resources
# Usage: ./update_translations.sh

set -e  # Exit on any error

echo "🌍 Panther Translation Update Script"
echo "===================================="

# Check if we're in the right directory
if [ ! -f "build.gradle" ] || [ ! -d "src/main/sourcemessages" ]; then
    echo "❌ Error: This script must be run from the MapsEngagementAndroidSDK root directory"
    echo "   Current directory: $(pwd)"
    echo "   Expected files: build.gradle, src/main/sourcemessages/"
    exit 1
fi

# Step 1: Run panther translate
echo ""
echo "📥 Step 1: Running panther translate..."
echo "--------------------------------------"

# Change to the directory where panther.cfg is located
cd src/main/sourcemessages/native

# Check if panther.cfg exists
if [ ! -f "panther.cfg" ]; then
    echo "❌ Error: panther.cfg not found in src/main/sourcemessages/native/"
    echo "   Please ensure the panther configuration is set up correctly."
    exit 1
fi

# Run panther translate
if command -v panther >/dev/null 2>&1; then
    echo "🔄 Running: panther translate"
    if panther translate; then
        echo "✅ Panther translate completed successfully"
        
        # Check if translations were generated
        if [ -d "translations" ] && [ "$(ls -A translations 2>/dev/null)" ]; then
            echo "📄 Generated translation files:"
            ls -1 translations/*.puff.json 2>/dev/null | head -5
            if [ "$(ls -1 translations/*.puff.json 2>/dev/null | wc -l)" -gt 5 ]; then
                echo "   ... and $(($(ls -1 translations/*.puff.json 2>/dev/null | wc -l) - 5)) more files"
            fi
        else
            echo "⚠️  No translation files found in translations directory"
        fi
    else
        echo "❌ Panther translate failed"
        exit 1
    fi
else
    echo "⚠️  Panther command not found in PATH"
    echo "   Checking for existing translation files..."
    
    if [ -d "translations" ] && [ "$(ls -A translations 2>/dev/null)" ]; then
        echo "✅ Found existing translation files, proceeding with conversion"
        ls -1 translations/*.puff.json 2>/dev/null | head -3
        if [ "$(ls -1 translations/*.puff.json 2>/dev/null | wc -l)" -gt 3 ]; then
            echo "   ... and more files"
        fi
    else
        echo "❌ No panther command found and no existing translation files"
        echo "   Please install panther or ensure translation files exist"
        exit 1
    fi
fi

# Go back to project root
cd ../../../..

# Step 2: Convert puff.json to XML
echo ""
echo "🔄 Step 2: Converting puff.json files to XML..."
echo "----------------------------------------------"

if [ ! -f "convert_puff_to_xml.py" ]; then
    echo "❌ Error: convert_puff_to_xml.py not found in project root"
    exit 1
fi

# Run the conversion script
if python3 convert_puff_to_xml.py; then
    echo ""
    echo "🎉 Translation update completed successfully!"
    echo ""
    echo "📊 Summary:"
    echo "  - Panther translate: ✅"
    echo "  - XML conversion: ✅"
    echo "  - Updated strings.xml files in multiple locales"
    echo ""
    echo "💡 Next steps:"
    echo "  - Review the changes: git diff"
    echo "  - Test your app with the new translations"
    echo "  - Commit the changes: git add . && git commit -m 'Update translations'"
else
    echo "❌ XML conversion failed"
    exit 1
fi

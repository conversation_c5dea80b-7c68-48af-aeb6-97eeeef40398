#!/bin/bash

echo "=== MapsEngagementAndroidSDK Translation Update Script ==="

# Note: This script assumes you have already run 'panther translate' manually
# from the src/main/sourcemessages/native directory

# Step 1: Copy translations to the expected location for the plugin
echo "Step 1: Copying translations to plugin directory..."
cp src/main/sourcemessages/native/translations/* src/main/translations/native/

# Step 2: Run the Android locale formatter
echo "Step 2: Running Android locale formatter..."
cd src/main/panther_scripts/lib
python3 androidLocaleFormatter.py
if [ $? -ne 0 ]; then
    echo "Error: Failed to run Android locale formatter"
    exit 1
fi

# Step 3: Build the project to trigger XML generation
echo "Step 3: Building project to generate XML files..."
cd ../../../..
./gradlew build
if [ $? -ne 0 ]; then
    echo "Error: Build failed"
    exit 1
fi

echo "=== Translation update completed successfully! ==="
echo "XML string resources have been updated in src/main/res/values-*/"

echo ""
echo "To update translations from <PERSON> in the future:"
echo "1. cd src/main/sourcemessages/native"
echo "2. panther translate"
echo "3. cd ../../.."
echo "4. ./update_translations.sh"

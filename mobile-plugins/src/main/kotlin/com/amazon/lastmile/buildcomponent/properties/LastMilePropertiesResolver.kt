package com.amazon.lastmile.buildcomponent.properties

import org.gradle.api.Project

fun Project.optionalStringValue(
    propertyName: String,
    defaultValue: String? = null,
): String? {
    return (properties.get(propertyName) as? String) ?: defaultValue
}

fun Project.optionalBooleanValue(
    propertyName: String,
    defaultValue: Boolean? = null,
): Boolean? {
    return (properties.get(propertyName) as? Boolean) ?: defaultValue
}

fun Project.stringValue(
    propertyName: String,
    defaultValue: String,
): String {
    return optionalStringValue(propertyName) ?: defaultValue
}

fun Project.booleanValue(
    propertyName: String,
    defaultValue: Boolean,
): Boolean {
    return optionalBooleanValue(propertyName) ?: defaultValue
}

val Project.isLmgpEnabled: Boolean get() {
    return stringValue("lmbc.lmgp", "enabled") == "enabled"
}

package com.amazon.lastmile.buildcomponent.convention.android

import com.amazon.lastmile.buildcomponent.android
import com.amazon.lastmile.buildcomponent.constant.LMBCConfiguration
import com.amazon.lastmile.buildcomponent.constant.LMBCNamedDomainContainer
import com.amazon.lastmile.buildcomponent.constant.LMBCPluginId
import com.amazon.lastmile.buildcomponent.convention.base.LastMilePeruRepositoriesPlugin
import com.amazon.lastmile.buildcomponent.convention.feature.LastMileAnnotationProcessorPlugin
import com.amazon.lastmile.buildcomponent.feature.LastMilePeruSyncPlugin
import com.android.build.api.dsl.ApplicationExtension
import org.gradle.api.Plugin
import org.gradle.api.Project
import org.gradle.api.logging.LogLevel

/**
 * Gradle Plugin applied for Android Applications.
 */
class LastMileAndroidAppPlugin : Plugin<Project> {
    override fun apply(target: Project) {
        target.logger.log(LogLevel.INFO, "Applying LastMileAndroidAppPlugin...")
        target.applyPlugins()
        target.configureAndroidApp()
        target.generateAppBundle()
    }

    private fun Project.applyPlugins() {
        plugins.apply(LMBCPluginId.AGP_APP)
        plugins.apply(LastMileAndroidBasePlugin::class.java)
        plugins.apply(LastMileAnnotationProcessorPlugin::class.java)
        plugins.apply(LMBCPluginId.KOTLIN_PARCELIZE)
        plugins.apply(LastMilePeruRepositoriesPlugin::class.java)
        plugins.apply(LastMilePeruSyncPlugin::class.java)
    }

    private fun Project.configureAndroidApp() {
        with(android) {
            when (this) {
                is ApplicationExtension -> {
                    defaultConfig {
                        targetSdk = LMBCConfiguration.TARGET_SDK_VERSION
                        multiDexEnabled = true
                    }

                    buildTypes {
                        getByName(LMBCNamedDomainContainer.DEBUG_VARIANT).apply {
                            isMinifyEnabled = false
                            isDebuggable = true
                        }

                        getByName(LMBCNamedDomainContainer.RELEASE_VARIANT).apply {
                            isMinifyEnabled = true
                            proguardFiles(
                                "conf/proguard/proguard-android.txt",
                                "conf/proguard/proguard-release.txt",
                            )
                            isDebuggable = false
                        }
                    }

                    sourceSets {
                        getByName("androidTest").java.srcDir("src/androidTest/kotlin")
                    }

                    packagingOptions {
                        resources.excludes.addAll(
                            setOf(
                                "META-INF/**",
                                "/**/*.java",
                                "**/*.lombok",
                                "findbugs/excludes.xml",
                                "build-data.properties",
                                "changelog.txt",
                                "latestchanges.html",
                                "lombok.config",
                                "lombok/**/*",
                                "AndroidManifest.xml",
                            ),
                        )
                        jniLibs.useLegacyPackaging = true
                    }
                }
            }
        }
    }

    private fun Project.generateAppBundle() {
        tasks.getByName(LMBCNamedDomainContainer.BUILD) { build ->
            build.dependsOn(LMBCNamedDomainContainer.BUNDLE)
        }
    }
}

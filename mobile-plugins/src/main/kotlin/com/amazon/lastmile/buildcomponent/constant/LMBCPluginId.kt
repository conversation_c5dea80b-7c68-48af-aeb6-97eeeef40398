package com.amazon.lastmile.buildcomponent.constant

/**
 * Plugins to get default project capabilities.
 */
object LMBCPluginId {
    // ANDROID
    const val AGP_APP = "com.android.application"
    const val AGP_LIB = "com.android.library"
    const val AGP_DYNAMIC_FEATURE = "com.android.dynamic-feature"

    // JAVA
    const val JAVA_LIBRARY = "java-library"

    // KOTLIN
    const val KOTLIN_ANDROID = "org.jetbrains.kotlin.android"
    const val KOTLIN_JVM = "org.jetbrains.kotlin.jvm"
    const val KAPT = "org.jetbrains.kotlin.kapt"
    const val KOTLIN_PARCELIZE = "kotlin-parcelize"

    const val KOTLIN_MULTIPLATFORM = "kotlin-multiplatform"
    const val KOTLIN_SERIALIZATION = "org.jetbrains.kotlin.plugin.serialization"
    const val KOTLIN_ATOMIC_FU = "kotlinx-atomicfu"

    // KSP
    const val KSP = "com.google.devtools.ksp"

    // App Platform
    const val APP_PLATFORM = "com.amazon.lastmile.app.platform.k2"

    // TEDP Platform
    const val TEDP_PLATFORM = "com.amazon.lastmile.tedp"

    // PERU
    const val PERU_BRAZIL_CLI = "software.amazon.peru.brazilcli"

    // MAVEN
    // This handles publication based on the project type.
    const val MAVEN_PUBLISH = "maven-publish"
    const val VANNI_MAVEN_PUBLISH = "com.vanniktech.maven.publish"
    const val VANNI_MAVEN_PUBLISH_BASE = "com.vanniktech.maven.publish.base"

    // PANTHER
    const val PANTHER_PRE_RESOURCE = "panther-pre-resource-compiler-helper"
    const val PANTHER_RESOURCE = "panther-resource-compiler"
    const val PANTHER_POST_RESOURCE = "panther-post-resource-compiler-helper"

    // QUALITY DEFAULTS
    const val CHECK_STYLE = "checkstyle"
    const val DETEKT = "io.gitlab.arturbosch.detekt"
    const val KOVER = "org.jetbrains.kotlinx.kover"
    const val KTLINT = "org.jlleitschuh.gradle.ktlint"
    const val SPOT_BUG = "com.github.spotbugs"
}

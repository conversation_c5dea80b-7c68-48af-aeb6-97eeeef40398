package com.amazon.lastmile.buildcomponent

import com.amazon.lastmile.buildcomponent.constant.LMBCPluginId
import com.amazon.lastmile.buildcomponent.properties.isLmgpEnabled
import org.gradle.api.Project
import org.gradle.api.plugins.AppliedPlugin
import org.gradle.api.plugins.PluginManager

val Project.isAndroidLibrary: Boolean
    get() = plugins.hasPlugin(LMBCPluginId.AGP_LIB)

val Project.isJavaLibrary: Boolean
    get() = plugins.hasPlugin(LMBCPluginId.JAVA_LIBRARY)

val Project.isAndroidProject: Boolean
    get() = plugins.hasPlugin(LMBCPluginId.AGP_APP)

val Project.isRoot: Boolean get() = this == rootProject

val Project.lmbcBuildDir: String
    get() = "$buildDir/lmbc-reports"

val Project.sourceResourceDir: String
    get() =
        if (isAndroidProject) {
            "app/src/main/sourcemessages"
        } else {
            "src/main/sourcemessages"
        }

val Project.translatedResourceDir: String
    get() =
        if (isAndroidProject) {
            "app/src/main/translations"
        } else {
            "src/main/translations"
        }

val Project.nativeResourceDir: String
    get() =
        if (isAndroidProject) {
            "app/src/main/res"
        } else {
            "src/main/res"
        }

fun Project.lmbcResourcesPath(): String = rootDir.parent + "LastMileBuildComponent/conf"

fun Project.libraryResourcePath(libName: String): String = if (!isAndroidProject) {
    rootDir.parent + "/$libName"
} else {
    rootDir.path
}

fun PluginManager.withPlugins(
    vararg pluginIds: String,
    action: (AppliedPlugin) -> Unit,
) {
    pluginIds.forEach {
        withPlugin(it, action)
    }
}

/**
 * Add LMGP Support to the [configurationType] provided.
 */
fun Project.addLMGPSupport(configurationType: String) {
    if (isLmgpEnabled) {
        dependencies.add(
            configurationType,
            dependencies.platform(
                "com.amazon.lastmile.gradleplatform:LastMileGradlePlatform:1.0.0",
            ),
        )
    }
}

/**
 * Resources are bundled in LMBC. To access resources from LMBC add LMBC as a dependency to the [configurationType].
 */
fun Project.addLMBCSupport(configurationType: String) {
    dependencies.add(
        configurationType,
        "com.amazon.lastmile.buildcomponent:mobile-plugins:1.0.0",
    )
}

package com.amazon.lastmile.buildcomponent.convention.kotlin

import com.amazon.lastmile.buildcomponent.constant.LMBCPluginId
import org.gradle.api.Project
import org.gradle.api.UnknownDomainObjectException
import org.jlleitschuh.gradle.ktlint.KtlintExtension

internal fun Project.configureKtlint() {
    plugins.apply(LMBCPluginId.KTLINT)
    extensions.getByType(KtlintExtension::class.java).apply {
        filter {
            it.exclude { element ->
                element.file.path.contains("generated/")
            }
        }
        version.set("1.2.1")
    }

    // Depending on the release to run the ktlintCheck
    @Suppress("SwallowedException")
    try {
        tasks.named("release").configure {
            it.dependsOn("ktlintCheck")
        }
    } catch (e: UnknownDomainObjectException) {
        logger.error("release tasks is not defined")
    }
}

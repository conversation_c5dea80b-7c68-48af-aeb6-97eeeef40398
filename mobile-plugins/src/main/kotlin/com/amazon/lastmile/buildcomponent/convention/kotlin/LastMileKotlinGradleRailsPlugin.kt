package com.amazon.lastmile.buildcomponent.convention.kotlin

import com.amazon.lastmile.buildcomponent.constant.LMBCConfiguration
import com.amazon.lastmile.buildcomponent.convention.base.LastMileJVMBasePlugin
import com.amazon.lastmile.buildcomponent.convention.kotlin.guardrails.DetektGuardRailsPlugin
import com.amazon.lastmile.buildcomponent.convention.kotlin.guardrails.KtlintGuardRailsPlugin
import com.amazon.lastmile.buildcomponent.kover.LastMileKoverPlugin
import org.gradle.api.Plugin
import org.gradle.api.Project
import org.jetbrains.kotlin.gradle.tasks.KotlinCompile

/**
 * Guard-rail tasks enforced on Kotlin based projects.
 */
class LastMileKotlinGradleRailsPlugin : Plugin<Project> {
    override fun apply(target: Project) {
        target.applyPlugins()
        target.addKotlinCompilerFlags()
    }

    private fun Project.applyPlugins() {
        plugins.apply(LastMileJVMBasePlugin::class.java)
        plugins.apply(LastMileKoverPlugin::class.java)
        plugins.apply(KtlintGuardRailsPlugin::class.java)
        plugins.apply(DetektGuardRailsPlugin::class.java)
    }

    private fun Project.addKotlinCompilerFlags() {
        tasks.withType(KotlinCompile::class.java).configureEach {
            it.kotlinOptions.jvmTarget = LMBCConfiguration.JAVA_VERSION.toString()
        }
    }
}

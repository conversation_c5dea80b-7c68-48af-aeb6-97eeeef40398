package com.amazon.lastmile.buildcomponent.convention.java

import com.amazon.lastmile.buildcomponent.constant.LMBCNamedDomainContainer
import com.amazon.lastmile.buildcomponent.kover.LastMileKoverPlugin
import org.gradle.api.Plugin
import org.gradle.api.Project

/**
 * Guard-rail tasks enforced on Java based projects.
 */
class LastMileJavaGuardRailsPlugin : Plugin<Project> {
    override fun apply(target: Project) {
        target.applyPlugins()
        // target.configureCheckStyle()
        // target.configureSpotBugs()
        target.createBuildTask()
    }

    private fun Project.applyPlugins() {
        plugins.apply(LastMileKoverPlugin::class.java)
    }

    /**
     * private fun Project.configureCheckStyle() {
     plugins.apply(LMBCPluginId.CHECK_STYLE)
     with(extensions.getByType(CheckstyleExtension::class.java)) {
     isIgnoreFailures = true
     configFile = file("conf/checkstyle/checkstyle-rules.xml")
     configProperties["checkstyle.suppression.filter"] = "conf/checkstyle/suppressions.xml"
     toolVersion = LMBCConfiguration.CHECK_STYLE_VERSION
     }

     tasks.withType(Checkstyle::class.java).forEach {
     it.reports.xml.required.set(true)
     it.reports.html.required.set(true)
     }

     tasks.register(LMBCNamedDomainContainer.CHECKSTYLE_RULES_SETUP, Copy::class.java) {
     it.from(file("${lmbcResourcesPath()}/checkstyle/checkstyle-rules.xml"))
     it.into("${libraryResourcePath(this.name)}/conf/checkstyle")
     it.fileMode = LMBCConfiguration.COPY_FILE_PERMISSION
     }
     tasks.register(LMBCNamedDomainContainer.CHECKSTYLE_SUPPRESSIONS_SETUP, Copy::class.java) {
     it.from(file("${lmbcResourcesPath()}/checkstyle/suppressions.xml"))
     it.into("${libraryResourcePath(this.name)}/conf/checkstyle")
     it.fileMode = LMBCConfiguration.COPY_FILE_PERMISSION
     }
     }
     private fun Project.configureSpotBugs() {
     plugins.apply(LMBCPluginId.SPOT_BUG)
     tasks.withType(SpotBugsTask::class.java) { spotBugs ->
     spotBugs.excludeFilter.set(file("conf/spotbugs/spotbugsExclude.xml"))
     spotBugs.ignoreFailures = true
     }

     with(extensions.getByType(SpotBugsExtension::class.java)) {
     ignoreFailures.set(true)
     effort.set(Effort.DEFAULT)
     reportLevel.set(Confidence.MEDIUM)
     }

     tasks.register(LMBCNamedDomainContainer.SPOTBUGS_SETUP, Copy::class.java) {
     it.from(file("${lmbcResourcesPath()}/spotbugs/spotbugsExclude.xml"))
     it.into("${libraryResourcePath(this.name)}/conf/spotbugs")
     it.fileMode = LMBCConfiguration.COPY_FILE_PERMISSION
     }
     }*/

    private fun Project.createBuildTask() {
        /**
         "check" task runs both "checkStyle" and "spotbugs"
         TODO - enable this task along with Java Guardrails.
         tasks.getByName(LMBCNamedDomainContainer.PRE_COMMIT_CODE_CHECKS) {
         it.dependsOn("check")
         }*/

        tasks.getByName(LMBCNamedDomainContainer.BUILD) {
            it.dependsOn("check")
        }
    }
}

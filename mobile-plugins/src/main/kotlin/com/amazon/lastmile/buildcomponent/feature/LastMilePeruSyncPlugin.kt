package com.amazon.lastmile.buildcomponent.feature

import com.amazon.lastmile.buildcomponent.getSourceDirectory
import org.gradle.api.Plugin
import org.gradle.api.Project
import org.gradle.api.logging.LogLevel
import java.io.File
import java.util.concurrent.TimeUnit

class LastMilePeruSyncPlugin : Plugin<Project> {
    override fun apply(target: Project) {
        val workspaceRoot = findWorkspaceRoot(target)
        val packageInfo = File("$workspaceRoot/packageInfo")
        lastModified(workspaceRoot)?.let { modified ->
            if (packageInfo.lastModified() > modified) {
                target.logger.log(LogLevel.WARN, "Message: Brazil metadata is synced again. Resetting cache TTL")
                target.configurations.all {
                    it.resolutionStrategy.cacheDynamicVersionsFor(0, TimeUnit.SECONDS)
                }
            } else {
                target.logger.log(LogLevel.WARN, "Message: using cached results")
                target.configurations.all {
                    it.resolutionStrategy.cacheDynamicVersionsFor(HOURS_IN_DAY, TimeUnit.HOURS)
                }
            }
        }
        storeLastModified(packageInfo.lastModified(), workspaceRoot)
    }

    private fun findWorkspaceRoot(project: Project): String {
        return "packageInfo".getSourceDirectory(project.rootDir) ?: project.rootDir.parentFile.parentFile.absolutePath
    }

    private fun storeLastModified(
        lastModified: Long,
        workspaceRoot: String,
    ) {
        File("$workspaceRoot/$LAST_MODIFIED_FILE").writeText("$lastModified")
    }

    private fun lastModified(workspaceRoot: String): Long? {
        val lastmodified = File("$workspaceRoot/$LAST_MODIFIED_FILE")
        return if (lastmodified.exists()) lastmodified.readText().toLongOrNull() else null
    }

    companion object {
        private const val LAST_MODIFIED_FILE = ".lastmodified"
        private const val HOURS_IN_DAY = 24
    }
}

package com.amazon.lastmile.buildcomponent.extension

import com.amazon.lastmile.buildcomponent.constant.LMBCConfiguration
import com.amazon.lastmile.buildcomponent.convention.kotlin.configureKtlint
import org.gradle.api.Project
import org.gradle.api.model.ObjectFactory
import org.gradle.api.provider.Property
import javax.inject.Inject

/**
 * Customize Code Quality behavior based on options provided.
 * Use this as a inner property as [LMBCExecutionExtension].
 * @see LMBCExecutionExtension
 *
 * Example:
 * codeQuality {
 *         ktlintVersion = "1.0.0"
 * }
 */
open class CodeQualityExtension @Inject constructor(
    objectFactory: ObjectFactory,
    var project: Project,
) {
    private val ktlinVersion: Property<String> = objectFactory.property(String::class.java).convention(
        LMBCConfiguration.KTLINT_0_31_0,
    )

    private val detektSource: Property<String> = objectFactory.property(
        String::class.java,
    ).convention(DetektSource.MODIFIED_FILES.value)

    private val ignoreDetekt: Property<Boolean> = objectFactory.property(Boolean::class.java).convention(false)

    fun setKtlintVersion(version: String) {
        ktlinVersion.set(version)
        ktlinVersion.disallowChanges()
        if (version == LMBCConfiguration.KTLINT_1_0_0) {
            // This is where we configure the default ktlint version
            project.configureKtlint()
        }
    }

    fun getKtlintVersion(): String = ktlinVersion.get()

    fun setDetektSource(source: String) {
        detektSource.set(source)
        detektSource.disallowChanges()
    }

    fun getDetektSource(): String = detektSource.get()

    fun setIgnoreDetekt(isIgnore: Boolean) {
        ignoreDetekt.set(isIgnore)
        ignoreDetekt.disallowChanges()
    }

    fun isIgnoreDetekt(): Boolean = ignoreDetekt.get()
}

enum class DetektSource(val value: String) {
    MODIFIED_FILES("modified"),
    ALL_FILES("all"),
}

package com.amazon.lastmile.buildcomponent

import org.gradle.api.Project
import java.io.ByteArrayOutputStream
import java.io.File

/**
 * @return true if running on local build. false if running dry-run or pipeline builds.
 */
fun isLocalBuild() = System.getenv("BRAZIL_PACKAGE_VERSION") == null

/**
 * Execute terminal commands.
 *
 * @param arguments command to be executed. pass each "space" separated argument as a new entry to list.
 * For example:
 * `git diff --name-only` will be passed in as
 * listOf("git", "diff","--name-only")
 *
 * @return plain terminal output in byte stream.
 */
fun Project.executeShellCommands(arguments: List<String>): ByteArrayOutputStream {
    val streamOutput = ByteArrayOutputStream()
    this.exec {
        it.commandLine(arguments)
        it.standardOutput = streamOutput
    }
    return streamOutput
}

/**
 * Looks for the "String" source directory by going through parents
 * and returns first directory that contains it.
 *
 * @return null if not found
 */
fun String.getSourceDirectory(startingDir: File) = generateSequence(startingDir) {
    it.parentFile
}.find {
    File(it, this).exists()
}?.absolutePath

package com.amazon.lastmile.buildcomponent.convention.feature

import com.amazon.lastmile.buildcomponent.addLMGPSupport
import com.amazon.lastmile.buildcomponent.constant.LMBCNamedDomainContainer
import com.amazon.lastmile.buildcomponent.constant.LMBCPluginId
import com.amazon.lastmile.buildcomponent.properties.isLmgpEnabled
import org.gradle.api.Plugin
import org.gradle.api.Project
import org.gradle.api.logging.LogLevel
import org.jetbrains.kotlin.gradle.internal.KaptWithoutKotlincTask
import org.jetbrains.kotlin.gradle.plugin.KaptExtension

/**
 * Gradle Plugin applied for Kotlin libraries.
 */
class LastMileAnnotationProcessorPlugin : Plugin<Project> {
    override fun apply(target: Project) {
        target.logger.log(LogLevel.INFO, "Applying LastMileAnnotationProcessorPlugin...")
        target.applyPlugins()
        target.enableGradleWorker()
    }

    private fun Project.applyPlugins() {
        plugins.apply(LMBCPluginId.KAPT)
        extensions.getByType(KaptExtension::class.java).apply {
            correctErrorTypes = true
        }
        configureDagger()
    }

    fun Project.enableGradleWorker() {
        tasks.withType(KaptWithoutKotlincTask::class.java).configureEach {
            it.kaptProcessJvmArgs.add("-Xmx512m")
        }
    }

    fun Project.configureDagger() {
        if (isLmgpEnabled) {
            addLMGPSupport(LMBCNamedDomainContainer.KAPT)
            addLMGPSupport(LMBCNamedDomainContainer.KAPT_TEST)
            dependencies.add(LMBCNamedDomainContainer.KAPT, "com.squareup.dagger:dagger-compiler")
            dependencies.add(LMBCNamedDomainContainer.KAPT_TEST, "com.squareup.dagger:dagger-compiler")
        }
    }
}

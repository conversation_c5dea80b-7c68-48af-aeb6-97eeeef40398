package com.amazon.lastmile.buildcomponent

import com.amazon.lastmile.app.platform.gradle.ModuleStructurePlugin.Companion.artifactId
import com.amazon.lastmile.app.platform.gradle.isUsingModuleStructure
import com.amazon.lastmile.buildcomponent.constant.LMBCNamedDomainContainer
import com.amazon.lastmile.buildcomponent.constant.LMBCPluginId
import com.amazon.lastmile.buildcomponent.properties.isLmgpEnabled
import com.vanniktech.maven.publish.AndroidMultiVariantLibrary
import com.vanniktech.maven.publish.MavenPublishBaseExtension
import org.gradle.api.Project

fun Project.addTestDependencies() {
    if (isLmgpEnabled) {
        dependencies.add(LMBCNamedDomainContainer.TEST_IMPL, "org.junit.jupiter:junit-jupiter")
        dependencies.add(LMBCNamedDomainContainer.TEST_IMPL, "junit:junit")
        dependencies.add(LMBCNamedDomainContainer.TEST_IMPL, "org.junit.jupiter:junit-jupiter-api")
        dependencies.add(LMBCNamedDomainContainer.TEST_IMPL, "org.junit.jupiter:junit-jupiter-engine")
    }
}

fun Project.setupAndroidLibPublication() {
    plugins.apply(LMBCPluginId.VANNI_MAVEN_PUBLISH_BASE)
    extensions.getByType(MavenPublishBaseExtension::class.java).apply {
        configure(AndroidMultiVariantLibrary(true, false))

        // This will set group and version from a gradle.properties file if available. With
        // this it's not required to set group and version for each submodule in particular
        // when using the recommended module structure.
        pomFromGradleProperties()
    }

    configureArtifactId()
}

fun Project.setupPublication() {
    plugins.apply(LMBCPluginId.VANNI_MAVEN_PUBLISH)
    configureArtifactId()
}

private fun Project.configureArtifactId() {
    if (isUsingModuleStructure()) {
        extensions.getByType(MavenPublishBaseExtension::class.java).apply {
            coordinates(
                artifactId = artifactId(),
            )
        }
    }
}

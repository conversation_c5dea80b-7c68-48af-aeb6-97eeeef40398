package com.amazon.lastmile.buildcomponent.convention.android

import com.amazon.lastmile.buildcomponent.constant.LMBCConfiguration
import com.amazon.lastmile.buildcomponent.constant.LMBCNamedDomainContainer
import com.amazon.lastmile.buildcomponent.constant.LMBCPluginId
import com.amazon.lastmile.buildcomponent.extension.LMBCInitializationExtension.applyAnnotation
import com.amazon.lastmile.buildcomponent.extension.LMBCInitializationExtension.applyKotlinParcelize
import com.amazon.lastmile.buildcomponent.extension.LMBCInitializationExtension.applyPublication
import com.android.build.api.dsl.LibraryExtension
import org.gradle.api.Plugin
import org.gradle.api.Project
import org.gradle.api.logging.LogLevel

/**
 * Gradle Plugin applied for Android libraries.
 */
class LastMileAndroidLibPlugin : Plugin<Project> {
    override fun apply(target: Project) {
        target.logger.log(LogLevel.INFO, "Applying LastMileAndroidLibPlugin...")
        target.applyPlugins()
        target.configureAndroidLib()
    }

    private fun Project.applyPlugins() {
        plugins.apply(LMBCPluginId.AGP_LIB)
        plugins.apply(LastMileAndroidBasePlugin::class.java)
        applyKotlinParcelize()
        applyAnnotation()
        applyPublication()
    }

    private fun Project.configureAndroidLib() {
        when (this) {
            is LibraryExtension -> {
                defaultConfig {
                    aarMetadata {
                        minCompileSdk = LMBCConfiguration.COMPILE_SDK_VERSION
                    }
                    testFixtures {
                        enable = true
                    }
                    multiDexEnabled = true

                    buildTypes {
                        getByName(LMBCNamedDomainContainer.DEBUG_VARIANT).apply {
                            isMinifyEnabled = false
                            isShrinkResources = true
                        }

                        getByName(LMBCNamedDomainContainer.RELEASE_VARIANT).apply {
                            isMinifyEnabled = false
                            isShrinkResources = true
                            proguardFiles(
                                "conf/proguard/proguard-android.txt",
                                "conf/proguard/proguard-release.txt",
                            )
                        }
                    }
                }
            }
        }
    }
}

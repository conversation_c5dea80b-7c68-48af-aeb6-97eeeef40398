package com.amazon.lastmile.buildcomponent.aggregation

import com.amazon.lastmile.buildcomponent.addLMBCSupport
import com.amazon.lastmile.buildcomponent.constant.LMBCConfiguration
import com.amazon.lastmile.buildcomponent.constant.LMBCNamedDomainContainer
import com.amazon.lastmile.buildcomponent.getSourceDirectory
import org.gradle.api.Plugin
import org.gradle.api.Project
import org.gradle.api.tasks.Copy
import java.io.File

/**
 * Plugin to perform PreCommit Hooks Tasks.
 */
class LastMileSetupPreCommitHookPlugin : Plugin<Project> {
    override fun apply(target: Project) {
        target.addGradleTasks()
    }

    private fun Project.addGradleTasks() {
        val preCommitConfigurationConfig = configurations.create(PRECOMMIT_CONFIGURATION)
        addLMBCSupport(preCommitConfigurationConfig.name)
        preCommitConfigurationConfig.isTransitive = false

        tasks.register(LMBCNamedDomainContainer.PRE_COMMIT_COPY_HOOKS, Copy::class.java) { copy ->
            val gitRepositoryPath = ".git".getSourceDirectory(File(System.getProperty("user.dir")))
            if (!gitRepositoryPath.isNullOrEmpty()) {
                project.logger.quiet("Copying precommit hooks..")
                copy.from(
                    project.resources.text.fromArchiveEntry(
                        configurations.getByName(PRECOMMIT_CONFIGURATION),
                        PRECOMMIT_HOOK_PATH,
                    ).asFile(),
                )
                copy.into("$gitRepositoryPath/.git/hooks")
                copy.fileMode = LMBCConfiguration.COPY_FILE_PERMISSION
            } else {
                project.logger.error("Could not find git path ${project.name}")
            }
        }

        afterEvaluate {
            tasks.getByName(LMBCNamedDomainContainer.LMBC_SETUP) {
                it.dependsOn(LMBCNamedDomainContainer.PRE_COMMIT_COPY_HOOKS)
            }
            tasks.getByName(LMBCNamedDomainContainer.BUILD) {
                it.dependsOn(LMBCNamedDomainContainer.PRE_COMMIT_COPY_HOOKS)
            }
        }
    }

    companion object {
        const val PRECOMMIT_CONFIGURATION = "preCommitConfiguration"
        const val PRECOMMIT_HOOK_PATH = "configurations/hooks/pre-commit"
    }
}

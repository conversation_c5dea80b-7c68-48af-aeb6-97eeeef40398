package com.amazon.lastmile.buildcomponent.aggregation

import com.amazon.lastmile.buildcomponent.executeShellCommands
import com.amazon.lastmile.buildcomponent.isLocalBuild
import org.gradle.api.Project

/**
 * Checks if the current execution directory is git or non-git.
 *
 * @return true if Git repo otherwise false.
 */
private fun Project.isGitRepo(): Bo<PERSON>an {
    if (!isLocalBuild()) {
        // GIT_DISCOVERY_ACROSS_FILESYSTEM not set for dry-run or pipeline builds.
        project.logger.quiet("Running on dry-run or pipeline build.")
        return false
    }
    val isGitRepoInBytes = executeShellCommands(listOf("git", "rev-parse", "--is-inside-work-tree"))
    val isGitRepo = isGitRepoInBytes.toString().trim().split("\n").getOrNull(0)
    return isGitRepo?.let { it == "true" } ?: run { false }
}

/**
 * Get Kotlin Modified files from Git.
 */
fun Project.getGitKtModifiedFiles(): List<String> {
    if (!isGitRepo()) {
        project.logger.quiet("Not a git repo to get git modified files.")
        return emptyList()
    }
    val result = mutableListOf<String>()
    for (each in getChangedFiles()) {
        if (each.endsWith(".kt") || each.endsWith(".kts")) {
            if (each.indexOf("src/") != -1) {
                result.add(each.substring(each.indexOf("src/")))
            }
        }
    }
    return result
}

/**
 * Returns git diff files from staged and un-staged status.
 */
private fun Project.getChangedFiles(): List<String> {
    val unStagedFilesInBytes = executeShellCommands(listOf("git", "diff", "--name-only"))
    val unStagedFiles = unStagedFilesInBytes.toString().trim().split("\n")
    val stagedFilesInBytes = executeShellCommands(listOf("git", "diff", "--name-only", "--staged"))
    val stagedFiles = stagedFilesInBytes.toString().trim().split("\n")
    return stagedFiles.plus(unStagedFiles)
}

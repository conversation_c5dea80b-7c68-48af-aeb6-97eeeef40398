package com.amazon.lastmile.buildcomponent.convention.kotlin.guardrails

import com.amazon.lastmile.buildcomponent.addLMBCSupport
import com.amazon.lastmile.buildcomponent.aggregation.getGitKtModifiedFiles
import com.amazon.lastmile.buildcomponent.constant.LMBCConfiguration
import com.amazon.lastmile.buildcomponent.constant.LMBCNamedDomainContainer
import com.amazon.lastmile.buildcomponent.constant.LMBCPluginId
import com.amazon.lastmile.buildcomponent.extension.DetektSource
import com.amazon.lastmile.buildcomponent.extension.LMBCMobileExtension.Companion.lmbc
import com.amazon.lastmile.buildcomponent.lmbcBuildDir
import io.gitlab.arturbosch.detekt.Detekt
import io.gitlab.arturbosch.detekt.extensions.DetektExtension
import org.gradle.api.Plugin
import org.gradle.api.Project
import java.io.File

/**
 * Detekt Plugin applied to Kotlin Libraries. This plugin is used to identify code smells.
 */
class DetektGuardRailsPlugin : Plugin<Project> {
    override fun apply(target: Project) {
        target.plugins.apply(LMBCPluginId.DETEKT)

        target.afterEvaluate {
            val detektExt = target.lmbc().codeQuality

            target.tasks.withType(Detekt::class.java).configureEach { detekt ->
                detekt.jvmTarget = LMBCConfiguration.JAVA_VERSION.toString()
                detekt.reports { detektReports ->
                    detektReports.html.required.set(true)
                    detektReports.txt.required.set(true)
                    detektReports.xml.required.set(false)
                    detektReports.md.required.set(false)
                }

                // Though these packages are excluded, LMBC need a 2-way confirmation from those packages to get excluded.
                detekt.ignoreFailures = target.name in excludePackages && detektExt.isIgnoreDetekt()
            }

            val detektConfiguration = target.configurations.create(DETEKT_RULES)
            detektConfiguration.isTransitive = false

            target.addLMBCSupport(detektConfiguration.name)

            with(target.extensions.getByType(DetektExtension::class.java)) {
                buildUponDefaultConfig = true
                config.from(
                    target.resources.text.fromArchiveEntry(
                        target.configurations.getByName(DETEKT_RULES),
                        DETEKT_RULE_PATH,
                    ),
                )

                if (detektExt.getDetektSource() != DetektSource.ALL_FILES.value) {
                    source.setFrom(target.getGitKtModifiedFiles())
                }
                reportsDir = File("${target.lmbcBuildDir}/detekt")
            }

            target.tasks.getByName(LMBCNamedDomainContainer.PRE_COMMIT_CODE_CHECKS) {
                it.dependsOn("detekt")
            }
        }
    }

    companion object {
        private const val DETEKT_RULES = "detektRules"
        private const val DETEKT_RULE_PATH = "configurations/detekt/detekt.yml"

        // Maps have MapboxV10 release and are on crunch-time. Fixing complex Detekt issues will put release date at jeopardy.
        // So, below packages are exempted for time-being. There will be a Policy Engine ticket cut to Maps team to remove this list.
        private val excludePackages = listOf("Astrogator", "GranTorino")
    }
}

package com.amazon.lastmile.buildcomponent.convention.android

import com.amazon.lastmile.app.platform.gradle.ModuleStructurePlugin.Companion.namespace
import com.amazon.lastmile.buildcomponent.android
import com.amazon.lastmile.buildcomponent.constant.LMBCConfiguration
import com.amazon.lastmile.buildcomponent.constant.LMBCPluginId
import com.amazon.lastmile.buildcomponent.convention.base.LastMilePeruRepositoriesPlugin
import com.amazon.lastmile.buildcomponent.feature.LastMilePeruSyncPlugin
import org.gradle.api.Plugin
import org.gradle.api.Project
import org.gradle.api.logging.LogLevel
import org.jetbrains.kotlin.gradle.dsl.JvmTarget
import org.jetbrains.kotlin.gradle.tasks.KotlinCompile

/**
 * Gradle Plugin applied for Android dynamic feature modules.
 */
class LastMileAndroidDynamicFeaturePlugin : Plugin<Project> {
    override fun apply(target: Project) {
        target.logger.log(LogLevel.INFO, "Applying LastMileAndroidAppPlugin...")
        target.applyPlugins()
        target.configureAndroidApp()
    }

    private fun Project.applyPlugins() {
        plugins.apply(LMBCPluginId.AGP_DYNAMIC_FEATURE)
        plugins.apply(LMBCPluginId.KOTLIN_ANDROID)
        plugins.apply(LMBCPluginId.MAVEN_PUBLISH)
        plugins.apply(LastMilePeruRepositoriesPlugin::class.java)
        plugins.apply(LastMilePeruSyncPlugin::class.java)
    }

    @Suppress("UnstableApiUsage")
    private fun Project.configureAndroidApp() {
        with(android) {
            compileSdk = LMBCConfiguration.COMPILE_SDK_VERSION
            namespace = namespace()

            defaultConfig {
                minSdk = LMBCConfiguration.MIN_SDK_VERSION
                missingDimensionStrategy("platform", "mobile")
                missingDimensionStrategy("mode", "All")
            }

            compileOptions {
                sourceCompatibility = LMBCConfiguration.JAVA_VERSION
                targetCompatibility = LMBCConfiguration.JAVA_VERSION
            }

            tasks.withType(KotlinCompile::class.java).configureEach {
                it.compilerOptions.jvmTarget.set(JvmTarget.fromTarget(LMBCConfiguration.JAVA_VERSION.toString()))
            }

            packagingOptions {
                resources.excludes.addAll(
                    setOf(
                        "META-INF/**",
                        "/**/*.java",
                        "**/*.lombok",
                        "**/NOTICE",
                        "findbugs/excludes.xml",
                        "build-data.properties",
                        "changelog.txt",
                        "latestchanges.html",
                        "lombok.config",
                        "lombok/**/*",
                        "AndroidManifest.xml",
                        "bundle.properties",
                        "kotlin/**",
                    ),
                )
            }
        }
    }
}

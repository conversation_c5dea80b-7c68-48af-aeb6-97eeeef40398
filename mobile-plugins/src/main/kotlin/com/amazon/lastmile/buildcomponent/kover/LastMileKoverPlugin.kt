package com.amazon.lastmile.buildcomponent.kover

import com.amazon.lastmile.buildcomponent.constant.LMBCPluginId
import kotlinx.kover.gradle.plugin.dsl.KoverProjectExtension
import org.gradle.api.Plugin
import org.gradle.api.Project

fun Project.isRobotsModule(): Boolean = name.endsWith("-robots")

private val Project.isRoot: <PERSON>olean get() = this == rootProject

private val Project.isLeafProject: Boolean get() = subprojects.isEmpty()

private val Project.isAndroidProject: Boolean
    get() = plugins.hasPlugin(LMBCPluginId.AGP_LIB) || plugins.hasPlugin(LMBCPluginId.AGP_APP)

class LastMileKoverPlugin : Plugin<Project> {
    override fun apply(target: Project) {
        if (!target.isRobotsModule()) {
            target.plugins.apply(LMBCPluginId.KOVER)
            target.configureCoverageReport()
            target.addTaskDependencies()

            if (target.isRoot) {
                target.configureRootProject()
            }
        }
    }

    private fun Project.configureRootProject() {
        // There is no separate merged Kover task anymore. Instead, we need to add dependencies
        // on all projects that should be included in the report. For more details see the
        // migration guide:
        //
        // https://github.com/Kotlin/kotlinx-kover/blob/v0.7.0/docs/gradle-plugin/migrations/migration-to-0.7.0.md#merge-reports-config-was-removed
        subprojects.filter { it.isLeafProject }.forEach { subproject ->
            dependencies.add("kover", subproject)
        }
    }

    private fun Project.variantName(): String {
        logger.error("PROJECT NAME: ${rootProject.name}, $name")
        return if (rootProject.name == "RabbitAndroidApp" && name == "app") {
            // Special Case, ideally, should not be the case
            "StandaloneDefaultPermAllRelease"
        } else if (isAndroidProject) {
            "debug"
        } else {
            "jvm"
        }
    }

    private fun Project.configureCoverageReport() {
        // By default Kover includes debug and release builds for Android projects. We only need the
        // debug build type and not release build type, because they're identical in regards to our
        // code coverage and we disable the release unit test variant anyways.
        //
        // This setup follows Kover's recommendation and creates a new "main" variant for all
        // projects. We then create a coverage report only for this variant.
        //
        // See: https://github.com/Kotlin/kotlinx-kover/issues/599
        extensions.configure(KoverProjectExtension::class.java) { extension ->
            val variant = "main"

            extension.currentProject { currentProjectConfig ->
                currentProjectConfig.createVariant(variant) { createConfig ->
                    createConfig.add(
                        variantNames = setOf(variantName()),
                        // Optional because for the root project the "jvm" variant doesn't exist
                        // and that's okay.
                        optional = isRoot,
                    )
                }
            }

            extension.reports { reports ->
                reports.variant(variant) { reportsConfig ->
                    reportsConfig.xml {
                        // Set the XML coverage report file to the same name used by Jacoco.
                        // Hopefully this is what Coverlay is looking for.
                        it.xmlFile.value(
                            layout.buildDirectory.file("$baseKoverCoveragePath/coverage.xml"),
                        )
                    }
                    reportsConfig.html {
                        it.htmlDir.value(layout.buildDirectory.dir(baseKoverCoveragePath))
                    }
                }

                reports.filters { reportFilters ->
                    reportFilters.excludes { filter ->
                        @Suppress("SpreadOperator")
                        filter.annotatedBy(*defaultKoverAnnotationFilter.toTypedArray())
                        filter.classes(defaultKoverClassFilter)
                    }
                }
            }
        }
    }

    private fun Project.addTaskDependencies() {
        setOf("koverHtmlReportMain", "koverXmlReportMain").forEach { taskName ->
            tasks.named("release").get().dependsOn(taskName)
        }
    }

    // Set this to the directory used by Jacoco, for continuity.
    private val baseKoverCoveragePath = "brazil-documentation/coverage"

    private val defaultKoverAnnotationFilter =
        setOf(
            "dagger.internal.DaggerGenerated",
            "dagger.Module",
        )

    private val defaultKoverClassFilter =
        setOf(
            "*Activity",
            "*Activity\$*",
            "*Fragment",
            "*Fragment\$*",
            // ViewBinding
            "*.databinding.*",
            "org.jetbrains.kover_android_groovy_example.BuildConfig",
            "*.BuildConfig",
            // DI-related
            "*.dagger.*",
            "*.*$${'$'}InjectAdapter",
            "*.config.constants.",
            "*.MetricConstants",
            "*_Factory*",
            "*_MembersInjector*",
            "*.Mock*",
        )
}

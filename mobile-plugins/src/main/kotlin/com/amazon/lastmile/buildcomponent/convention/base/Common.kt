package com.amazon.lastmile.buildcomponent.convention.base

import com.amazon.lastmile.buildcomponent.aggregation.LastMileSetupPreCommitHookPlugin
import com.amazon.lastmile.buildcomponent.constant.LMBCNamedDomainContainer
import org.gradle.api.Project

fun Project.setupLMBCTasks() {
    // TODO: Need to evaluate whether these are applicable for KMP Projects. If they do we can invoke them. For now, not enabling them for KMP
    plugins.apply(LastMileSetupPreCommitHookPlugin::class.java)
    tasks.register(LMBCNamedDomainContainer.LMBC_SETUP)
    tasks.register(LMBCNamedDomainContainer.PRE_COMMIT_CODE_CHECKS)
}

package com.amazon.lastmile.buildcomponent.convention.feature

import com.amazon.gradle.android.PantherPostResourceCompilerHelperExtension
import com.amazon.gradle.android.PantherPreResourceCompilerHelperExtension
import com.amazon.lastmile.buildcomponent.constant.LMBCConfiguration
import com.amazon.lastmile.buildcomponent.constant.LMBCNamedDomainContainer
import com.amazon.lastmile.buildcomponent.constant.LMBCPluginId
import com.amazon.lastmile.buildcomponent.libraryResourcePath
import com.amazon.lastmile.buildcomponent.lmbcResourcesPath
import com.amazon.lastmile.buildcomponent.nativeResourceDir
import com.amazon.lastmile.buildcomponent.sourceResourceDir
import com.amazon.lastmile.buildcomponent.translatedResourceDir
import com.amazon.panther.buildtool.android.PantherResourceCompilerExtension
import org.gradle.api.Plugin
import org.gradle.api.Project
import org.gradle.api.tasks.Copy
import java.io.File
import java.util.Properties

/**
 * Panther Plugin : https://w.amazon.com/bin/view/TransportationByAmazon/OnMyWay/RabbitDroid/PantherDevWorkflow/
 *
 * This Plugin is a conversion of groovy script into Custom Gradle Plugin.
 *
 * No new logic is added in this.
 */
class LastMilePantherPlugin : Plugin<Project> {
    override fun apply(target: Project) {
        target.createPantherTasks()
        target.pantherSetup()
    }

    private fun Project.createPantherTasks() {
        tasks.register("fetch_panther") {
            it.doLast {
                fetchPantherScriptTask()
            }
        }
        tasks.register("pull_translations") {
            it.doLast {
                createPullTranslationTask()
            }
        }
        tasks.register("check_manual_edit_xml_files") {
            it.doLast {
                manualEditXmlFilesTask()
            }
        }
        tasks.register("translation_status_update") {
            it.doLast {
                translationStatusUpdateTask()
            }
        }
        pantherExecute()
    }

    private fun Project.fetchPantherScriptTask() {
        exec { exec ->
            exec.isIgnoreExitValue = false
            exec.commandLine = listOf("./fetch_panther_scripts.sh")
        }
    }

    private fun Project.createPullTranslationTask() {
        exec { exec ->
            exec.isIgnoreExitValue = true
            exec.workingDir = File("src/main")
            exec.commandLine =
                listOf(
                    "panther",
                    "translate",
                )
        }
    }

    private fun Project.manualEditXmlFilesTask() {
        exec { exec ->
            exec.isIgnoreExitValue = false
            exec.commandLine = listOf("./check_manual_edit_xml_files.sh")
        }
    }

    private fun Project.translationStatusUpdateTask() {
        logger.info("Translation process completed")
    }

    private fun Project.pantherExecute() {
        afterEvaluate {
            if (rootProject.file("local.properties").exists()) {
                val properties = Properties()
                val inputStream = rootProject.file("local.properties").inputStream()
                properties.load(inputStream)
                if ((properties.getProperty("auto_pull_translations") == "enabled")) {
                    project.logger.info("-------------Checking for manually edited string xml files")
                    manualEditXmlFilesTask()
                    project.logger.info("-------------Fetching latest panther scripts")
                    fetchPantherScriptTask()
                    project.logger.info("pull_translations is enabled.")
                    project.logger.info("-------------Pulling translations from Panther")
                    createPullTranslationTask()

                    plugins.apply(LMBCPluginId.PANTHER_PRE_RESOURCE)
                    plugins.apply(LMBCPluginId.PANTHER_RESOURCE)
                    plugins.apply(LMBCPluginId.PANTHER_POST_RESOURCE)

                    with(extensions.getByType(PantherPreResourceCompilerHelperExtension::class.java)) {
                        pantherScriptDir = "src/main/panther_scripts/lib"
                        tagFetcher = "preImport.py"
                        androidLocaleFormatter = "androidLocaleFormatter.py"
                    }

                    with(extensions.getByType(PantherResourceCompilerExtension::class.java)) {
                        pantherSourceResourceDir = sourceResourceDir
                        pantherTranslatedResourceDir = translatedResourceDir
                        androidNativeResourceDir = nativeResourceDir
                    }

                    with(extensions.getByType(PantherPostResourceCompilerHelperExtension::class.java)) {
                        pantherScriptDir = "src/main/panther_scripts/lib"
                        tagReplacer = "postImport.py"
                        pantherLocaleFormatter = "pantherLocaleFormatter.py"
                    }
                } else {
                    project.logger.info("pull_translations is disabled.")
                }
            }
        }
    }

    private fun Project.pantherSetup() {
        tasks.register(LMBCNamedDomainContainer.PANTHER_FETCH, Copy::class.java) {
            it.from(file("${lmbcResourcesPath()}/panther/fetch_panther_scripts.sh"))
            it.into("${libraryResourcePath(this.name)}/")
            it.fileMode = LMBCConfiguration.COPY_FILE_PERMISSION
        }

        tasks.register(LMBCNamedDomainContainer.PANTHER_CHECK_EDIT, Copy::class.java) {
            it.from(file("${lmbcResourcesPath()}/panther/check_manual_edit_xml_files.sh"))
            it.into("${libraryResourcePath(this.name)}/")
            it.fileMode = LMBCConfiguration.COPY_FILE_PERMISSION
        }

        tasks.register(LMBCNamedDomainContainer.PANTHER_PULL, Copy::class.java) {
            it.from(file("${lmbcResourcesPath()}/panther/pull_translation.sh"))
            it.into("${libraryResourcePath(this.name)}/")
            it.fileMode = LMBCConfiguration.COPY_FILE_PERMISSION
        }
    }
}

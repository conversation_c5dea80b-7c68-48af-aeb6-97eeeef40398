package com.amazon.lastmile.buildcomponent.constant

import org.gradle.api.JavaVersion

/**
 * Configurations for all the build tools.
 */
object LMBCConfiguration {
    // ANDROID
    const val COMPILE_SDK_VERSION = 34
    const val NDK_VERSION = "23.0.7599858"
    const val MIN_SDK_VERSION = 30
    const val TARGET_SDK_VERSION = 34

    // BUILD
    const val CHECK_STYLE_VERSION = "8.17"

    // JAVA
    val JAVA_VERSION = JavaVersion.VERSION_11

    // COPY FILE MODE
    // https://github.com/gradle/kotlin-dsl-samples/issues/1412
    const val COPY_FILE_PERMISSION = 0b111101101

    // Supported KTLINT VERSIONS
    const val KTLINT_0_31_0 = "0.31.0"
    const val KTLINT_1_0_0 = "1.0.0"
}

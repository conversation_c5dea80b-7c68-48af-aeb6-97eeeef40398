package com.amazon.lastmile.buildcomponent.extension

import com.amazon.lastmile.buildcomponent.constant.LMBCPluginId
import com.amazon.lastmile.buildcomponent.convention.base.LastMilePeruRepositoriesPlugin
import com.amazon.lastmile.buildcomponent.convention.feature.LastMileAnnotationProcessorPlugin
import com.amazon.lastmile.buildcomponent.isAndroidLibrary
import com.amazon.lastmile.buildcomponent.properties.stringValue
import com.amazon.lastmile.buildcomponent.setupAndroidLibPublication
import org.gradle.api.Project

/**
 * Gradle Properties are initialized during project initialization phase.
 * Properties in here will be used to configure project.
 * Properties defined in [LMBCExecutionExtension] will be used during execution phase.
 * also @see [LMBCExecutionExtension]
 *
 * Implementation of `gradle.properties` example:
 *
 * lmbc.publication=maven
 * lmbc.annotation=kapt
 */
object LMBCInitializationExtension {
    private const val PUBLICATION_TYPE = "lmbc.publication"
    private const val ANNOTATION_TYPE = "lmbc.annotation"
    private const val PARCELABLE_TYPE = "lmbc.parcelize"

    fun Project.applyAnnotation() {
        val isKaptEnabled =
            stringValue(ANNOTATION_TYPE, AnnotationType.KAPT.value) == AnnotationType.KAPT.value
        if (isKaptEnabled) {
            plugins.apply(LastMileAnnotationProcessorPlugin::class.java)
        }
    }

    fun Project.applyKotlinParcelize() {
        val isKotlinParcelizeEnabled =
            stringValue(
                PARCELABLE_TYPE,
                ParcelableType.KOTLIN_PARCELIZE.value,
            ) == ParcelableType.KOTLIN_PARCELIZE.value
        if (isKotlinParcelizeEnabled) {
            plugins.apply(LMBCPluginId.KOTLIN_PARCELIZE)
        }
    }

    fun Project.applyPublication() {
        when (stringValue(PUBLICATION_TYPE, PublicationType.LMBC_NATIVE.value)) {
            PublicationType.MAVEN.value -> {
                plugins.apply(LMBCPluginId.MAVEN_PUBLISH)
            }

            PublicationType.LMBC_NATIVE.value -> {
                plugins.apply(LastMilePeruRepositoriesPlugin::class.java)
                if (isAndroidLibrary) {
                    setupAndroidLibPublication()
                }
            }

            PublicationType.NONE.value -> {
                plugins.apply(LastMilePeruRepositoriesPlugin::class.java)
            }
        }
    }
}

enum class AnnotationType(val value: String) {
    NONE("none"),
    KAPT("kapt"),
}

enum class PublicationType(val value: String) {
    NONE("none"),
    MAVEN("maven"),
    LMBC_NATIVE("lmbcNative"),
}

enum class ParcelableType(val value: String) {
    NONE("none"),
    KOTLIN_PARCELIZE("kotlin-parcelize"),
}

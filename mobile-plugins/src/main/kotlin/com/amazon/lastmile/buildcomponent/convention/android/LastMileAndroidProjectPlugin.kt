package com.amazon.lastmile.buildcomponent.convention.android

import org.gradle.api.Plugin
import org.gradle.api.Project
import org.gradle.api.logging.LogLevel

/**
 * Android top-level plugin that contains configuration common to all sub-projects/modules.
 * TODO: Delete this
 */
class LastMileAndroidProjectPlugin : Plugin<Project> {
    override fun apply(target: Project) {
        target.logger.log(LogLevel.INFO, "Applying LastMileAndroidProjectPlugin...")
    }
}

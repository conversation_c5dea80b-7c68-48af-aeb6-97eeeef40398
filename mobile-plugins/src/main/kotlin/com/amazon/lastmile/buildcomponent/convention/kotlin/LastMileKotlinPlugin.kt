package com.amazon.lastmile.buildcomponent.convention.kotlin

import com.amazon.lastmile.buildcomponent.addTestDependencies
import com.amazon.lastmile.buildcomponent.base.setupWireRepositories
import com.amazon.lastmile.buildcomponent.constant.LMBCConfiguration
import com.amazon.lastmile.buildcomponent.constant.LMBCPluginId
import com.amazon.lastmile.buildcomponent.setupPublication
import org.gradle.api.Plugin
import org.gradle.api.Project
import org.gradle.api.logging.LogLevel
import org.gradle.api.plugins.JavaPluginExtension
import org.gradle.api.tasks.testing.Test

/**
 * Gradle Plugin applied for Kotlin libraries.
 */
class LastMileKotlinPlugin : Plugin<Project> {
    override fun apply(target: Project) {
        target.logger.log(LogLevel.INFO, "Applying LastMileKotlinPlugin...")
        target.applyPlugins()
        target.configureJavaVersion()
        target.addJavaSourceSets()
        target.addTestDependencies()
        target.setupPublication()
        target.setupWireRepositories()
        target.configureUnitTests()
    }

    private fun Project.applyPlugins() {
        plugins.apply(LMBCPluginId.KOTLIN_JVM)
        plugins.apply(LMBCPluginId.JAVA_LIBRARY)
        plugins.apply(LastMileKotlinGradleRailsPlugin::class.java)
    }

    private fun Project.configureJavaVersion() {
        with(extensions.getByType(JavaPluginExtension::class.java)) {
            sourceCompatibility = LMBCConfiguration.JAVA_VERSION
            targetCompatibility = LMBCConfiguration.JAVA_VERSION
        }
    }

    private fun Project.configureUnitTests() {
        tasks.withType(Test::class.java).configureEach {
            it.maxParallelForks = (Runtime.getRuntime().availableProcessors() / 2).coerceAtLeast(1)
        }
    }

    private fun Project.addJavaSourceSets() {
        extensions.getByType(JavaPluginExtension::class.java).apply {
            sourceSets.getByName("main") {
                it.java.srcDir("src/main/kotlin")
                it.java.srcDir("src/main/java")
            }
            sourceSets.getByName("test") {
                it.java.srcDir("src/test/kotlin")
                it.java.srcDir("src/test/java")
            }
        }
    }
}

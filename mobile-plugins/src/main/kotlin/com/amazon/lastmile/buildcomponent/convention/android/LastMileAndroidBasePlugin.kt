package com.amazon.lastmile.buildcomponent.convention.android

import com.amazon.lastmile.buildcomponent.addLMGPSupport
import com.amazon.lastmile.buildcomponent.addTestDependencies
import com.amazon.lastmile.buildcomponent.android
import com.amazon.lastmile.buildcomponent.base.setupReleaseTasks
import com.amazon.lastmile.buildcomponent.constant.LMBCConfiguration
import com.amazon.lastmile.buildcomponent.constant.LMBCNamedDomainContainer
import com.amazon.lastmile.buildcomponent.constant.LMBCPluginId
import com.amazon.lastmile.buildcomponent.convention.java.LastMileJavaGuardRailsPlugin
import com.amazon.lastmile.buildcomponent.convention.kotlin.LastMileKotlinGradleRailsPlugin
import com.amazon.lastmile.buildcomponent.mobile_plugins.BuildConfig
import com.amazon.lastmile.buildcomponent.properties.isLmgpEnabled
import com.amazon.lastmile.buildcomponent.withPlugins
import org.gradle.api.Plugin
import org.gradle.api.Project

/**
 * Android base plugin that contains common configurations, plugins, guard rails used by both App
 * and Libraries.
 */
class LastMileAndroidBasePlugin : Plugin<Project> {
    override fun apply(target: Project) {
        target.applyPluginManager()
        target.addDependencies()
        target.setupReleaseTasks()
        target.applyPlugins()
        target.setupProguard()
        target.configureTestParallelTests()
        target.addTestDependencies()
        target.configureJavaCompile()
        target.addDataBinding()
        if (target.isLmgpEnabled) {
            // TODO: It does not make sense to apply these if the lmgp is disabled.
            target.addForceResolutions()
        }
    }

    private fun Project.applyPluginManager() {
        pluginManager.withPlugins(
            LMBCPluginId.AGP_APP,
            LMBCPluginId.AGP_LIB,
        ) {
            configureAndroid()
        }
    }

    private val overrideVersionMap =
        mapOf(
            "androidx.sqlite:sqlite-framework" to "2.4.0",
            "androidx.sqlite:sqlite" to "2.4.0",
            "org.checkerframework:checker-compat-qual" to "2.3.0",
            "com.squareup.okhttp3:okhttp" to "4.9.3",
            "androidx.constraintlayout:constraintlayout" to "1.1.2",
            "com.google.android.material:material" to "1.2.1",
            "androidx.coordinatorlayout:coordinatorlayout" to "1.0.0",
            "androidx.recyclerview:recyclerview" to "1.0.0",
            "org.jetbrains.kotlin:kotlin-stdlib" to BuildConfig.KOTLIN_VERSION,
            "org.jetbrains.kotlin:kotlin-stdlib-common" to BuildConfig.KOTLIN_VERSION,
            "org.jetbrains.kotlin:kotlin-stdlib-jdk8" to BuildConfig.KOTLIN_VERSION,
            "org.jetbrains.kotlin:kotlin-reflect" to BuildConfig.KOTLIN_VERSION,
            "org.jetbrains.kotlin:kotlin-parcelize-runtime" to BuildConfig.KOTLIN_VERSION,
            "androidx.annotation:annotation" to "1.1.0",
            "androidx.annotation:annotation-experimental" to "1.1.0",
            "com.google.code.gson:gson" to "2.8.5",
            "com.fasterxml.jackson.core:jackson-databind" to "2.10.5",
            "com.fasterxml.jackson.core:jackson-core" to "2.10.5",
            "com.fasterxml.jackson.core:jackson-annotations" to "2.10.5",
            "com.fasterxml.jackson.dataformat:jackson-dataformat-cbor" to "2.10.5",
            "commons-codec:commons-codec" to "1.6",
            "org.apache.httpcomponents:httpclient" to "4.5",
            // compose:runtime 1.6+ requires SDK 34+
            "androidx.compose.runtime:runtime" to "1.5.4",
            "androidx.compose.runtime:runtime-android" to "1.5.4",
        )

    private fun Project.addForceResolutions() {
        configurations.all { configuration ->
            configuration.resolutionStrategy.eachDependency { dependency ->
                val details = dependency.requested
                if (overrideVersionMap.containsKey("${details.group}:${details.name}")) {
                    dependency.useVersion(overrideVersionMap["${details.group}:${details.name}"]!!)
                }
            }
            configuration.resolutionStrategy.dependencySubstitution.apply {
                mapOf(
                    "com.amazon.mshop:DeviceClientMetricsAndroid3rdParty:1.0" to
                        "com.amazon.client.metrics:DeviceClientMetricsAndroid3rdParty:1.3",
                    "com.amazon.rabbitperubrazilimport:AWSJavaCredentialInterfaces-1.0" to
                        "com.amazonaws:aws-android-sdk-core:2.19.4",
                ).forEach { (actual, replacement) ->
                    substitute(module(actual))
                        .using(module(replacement))
                }
            }
        }
    }

    // Default gradle dependencies added on a sample Android project.
    private fun Project.addDependencies() {
        // TODO: Disabling LMGP should reflect across the entire project. Ideally this should have been at a location to control based on LMGP.
        if (isLmgpEnabled) {
            addLMGPSupport(LMBCNamedDomainContainer.ANDROID_TEST_IMPL)
            addLMGPSupport(LMBCNamedDomainContainer.COMPILE_ONLY)
            addLMGPSupport(LMBCNamedDomainContainer.TEST_COMPILE_ONLY)

            dependencies.add(LMBCNamedDomainContainer.IMPLEMENTATION, "androidx.core:core-ktx")
            dependencies.add(LMBCNamedDomainContainer.IMPLEMENTATION, "androidx.appcompat:appcompat")
            dependencies.add(
                LMBCNamedDomainContainer.IMPLEMENTATION,
                "androidx.constraintlayout:constraintlayout",
            )
            dependencies.add(
                LMBCNamedDomainContainer.IMPLEMENTATION,
                "androidx.databinding:databinding-adapters",
            )
            dependencies.add(LMBCNamedDomainContainer.ANDROID_TEST_IMPL, "androidx.test.ext:junit")
            dependencies.add(
                LMBCNamedDomainContainer.ANDROID_TEST_IMPL,
                "androidx.test.espresso:espresso-core",
            )
            dependencies.add(LMBCNamedDomainContainer.COMPILE_ONLY, "com.squareup.dagger:dagger")
            dependencies.add(LMBCNamedDomainContainer.COMPILE_ONLY, "org.projectlombok:lombok")
            dependencies.add(LMBCNamedDomainContainer.TEST_COMPILE_ONLY, "com.squareup.dagger:dagger")
            dependencies.add(LMBCNamedDomainContainer.TEST_COMPILE_ONLY, "org.projectlombok:lombok")
        }
    }

    private fun Project.applyPlugins() {
        plugins.apply(LMBCPluginId.KOTLIN_ANDROID)
        plugins.apply(LastMileJavaGuardRailsPlugin::class.java)
        plugins.apply(LastMileKotlinGradleRailsPlugin::class.java)
    }

    private fun Project.configureAndroid() {
        with(android) {
            compileSdk = LMBCConfiguration.COMPILE_SDK_VERSION
            ndkVersion = LMBCConfiguration.NDK_VERSION

            defaultConfig {
                minSdk = LMBCConfiguration.MIN_SDK_VERSION
                missingDimensionStrategy("platform", "mobile")
                missingDimensionStrategy("mode", "All")
            }

            lint {
                disable +=
                    mutableSetOf(
                        "MissingTranslation",
                        "MissingQuantity",
                        "ImpliedQuantity",
                        "NewApi",
                    )
            }

            tasks.withType(org.jetbrains.kotlin.gradle.tasks.KotlinCompile::class.java)
                .configureEach {
                    it.kotlinOptions {
                        jvmTarget = LMBCConfiguration.JAVA_VERSION.toString()
                    }
                }

            testOptions {
                unitTests {
                    isReturnDefaultValues = true
                    isIncludeAndroidResources = true
                    all {
                        val wireRepoUrl = project.property("wireReadRepositoryUrl") as java.net.URI
                        it.systemProperty("robolectric.dependency.repo.url", wireRepoUrl)
                        it.systemProperty("robolectric.dependency.repo.id", "wireRepo")
                    }
                }
            }

            compileOptions {
                sourceCompatibility = LMBCConfiguration.JAVA_VERSION
                targetCompatibility = LMBCConfiguration.JAVA_VERSION
            }

            buildTypes {
                maybeCreate(LMBCNamedDomainContainer.DEBUG_VARIANT)
                maybeCreate(LMBCNamedDomainContainer.RELEASE_VARIANT)
            }

            sourceSets {
                getByName("debug").java.srcDir("src/debug/kotlin")
                getByName("release").java.srcDir("src/release/kotlin")
                getByName("main").java.srcDir("src/main/kotlin")
                getByName("test").java.srcDir("src/test/kotlin")
                getByName("androidTest").java.srcDir("src/androidTest/kotlin")
            }
        }
    }

    companion object {
        const val MAX_TEST_CASES = 100L
    }
}

package com.amazon.lastmile.buildcomponent.constant

import org.gradle.api.plugins.JavaPlugin

/**
 *  Constant element types for tasks and configurations.
 *
 *  These elements are defined by <PERSON><PERSON><PERSON> and created for custom LMBC tasks.
 */
object LMBCNamedDomainContainer {
    //  TASKS
    const val BUILD = "build"
    const val BUNDLE = "bundle"
    const val PUBLISH = "publish"
    const val RELEASE = "release"

    // CONFIGURATIONS
    const val ANDROID_TEST_IMPL = "androidTestImplementation"
    const val ANNOTATION_PROCESSOR = "annotationProcessor"
    const val API = JavaPlugin.API_CONFIGURATION_NAME
    const val COMPILE_ONLY = "compileOnly"
    const val IMPLEMENTATION = JavaPlugin.IMPLEMENTATION_CONFIGURATION_NAME
    const val KAPT = "kapt"
    const val KAPT_TEST = "kaptTest"
    const val KSP = "ksp"
    const val TEST_ANNOTATION_PROCESSOR = "testAnnotationProcessor"
    const val TEST_COMPILE_ONLY = "testCompileOnly"
    const val TEST_IMPL = JavaPlugin.TEST_IMPLEMENTATION_CONFIGURATION_NAME

    // CUSTOM TASKS
    const val CHECKSTYLE_RULES_SETUP = "checkStyleRulesSetup"
    const val CHECKSTYLE_SUPPRESSIONS_SETUP = "checkStyleSuppressionsSetup"
    const val DETEKT_SETUP = "detektSetup"
    const val PRE_COMMIT_CODE_CHECKS = "preCommitCodeChecks"
    const val PANTHER_CHECK_EDIT = "pantherCheckEdit"
    const val PANTHER_FETCH = "pantherFetch"
    const val PANTHER_PULL = "pantherPull"
    const val PRE_COMMIT_COPY_HOOKS = "preCommitCopyHooks"
    const val PROGUARD_ANDROID_SETUP = "proGuardDebugSetup"
    const val PROGUARD_RELEASE_SETUP = "proGuardReleaseSetup"
    const val LMBC_SETUP = "lmbcSetup"
    const val SPOTBUGS_SETUP = "spotBugSetup"

    // BUILD VARIANTS
    const val DEBUG_VARIANT = "debug"
    const val RELEASE_VARIANT = "release"

    // LMBC Extension Property
    const val LMBC_EXT_PROPERTY = "lmbc"
}

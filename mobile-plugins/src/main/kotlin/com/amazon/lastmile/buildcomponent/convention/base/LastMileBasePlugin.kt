package com.amazon.lastmile.buildcomponent.convention.base

import com.amazon.lastmile.buildcomponent.addLMGPSupport
import com.amazon.lastmile.buildcomponent.base.setupReleaseTasks
import com.amazon.lastmile.buildcomponent.constant.LMBCPluginId
import com.amazon.lastmile.buildcomponent.extension.LMBCMobileExtension.Companion.createExtension
import com.amazon.lastmile.buildcomponent.isLocalBuild
import com.amazon.lastmile.buildcomponent.mobile_plugins.BuildConfig
import com.android.build.gradle.tasks.MergeSourceSetFolders
import com.android.build.gradle.tasks.ProcessAndroidResources
import org.gradle.api.DefaultTask
import org.gradle.api.GradleException
import org.gradle.api.Plugin
import org.gradle.api.Project
import org.gradle.api.file.Directory
import org.gradle.api.file.DirectoryProperty
import org.gradle.api.file.FileSystemOperations
import org.gradle.api.file.RegularFileProperty
import org.gradle.api.provider.Property
import org.gradle.api.provider.Provider
import org.gradle.api.tasks.Input
import org.gradle.api.tasks.InputFile
import org.gradle.api.tasks.OutputDirectory
import org.gradle.api.tasks.OutputFile
import org.gradle.api.tasks.TaskAction
import org.gradle.workers.WorkAction
import org.gradle.workers.WorkParameters
import org.gradle.workers.WorkerExecutor
import org.jetbrains.kotlin.gradle.utils.API
import java.io.File
import java.time.Instant
import java.time.LocalDateTime
import java.time.ZoneOffset
import javax.inject.Inject
import kotlin.system.exitProcess

/**
 * LMBC base plugin that sets the minimum plugin tasks for convention plugins.
 */
open class LastMileBasePlugin : Plugin<Project> {
    override fun apply(target: Project) {
        target.overrideAppPlatform()
        target.setupLMBCTasks()
        target.createExtension()
        target.applyPlugins()
        target.setupReleaseTasks()
        target.addDependencies()
        target.extensions.add("peru", BuildPathUtil)
        BuildPathUtil.setTarget(target)
        target.showBrazilDeprecationWarning()
    }

    private fun Project.applyPlugins() {
        plugins.apply(LMBCPluginId.MAVEN_PUBLISH)
    }

    private fun Project.addDependencies() {
        addLMGPSupport(API)
    }

    private fun Project.showBrazilDeprecationWarning() {
        val isNowAfterEndOfMay22thUtc = isNowAfterEndOfMay22thUtc()
        if (isPackageExempted()) return
        if (isBrazilPackage() && isNowAfterEndOfMay22thUtc && isLocalBuild()) {
            val deprecationMessage =
                buildString {
                    append("\n")
                    append("=".repeat(LENGTH_OF_MESSAGE)).append("\n")
                    append("FAILING BUILD AS THIS PACKAGE STILL USE BRAZIL\n")
                    append("Brazil is deprecated.\n")
                    append("1. Please remove the brazil configurations.\n")
                    append("2. Execute brazil-build deprecated-brazil.\n")
                    append("3. Acnowledge each step and make changes.\n")
                    append("4. Ignore the build failure towards the end.\n")
                    append("=".repeat(LENGTH_OF_MESSAGE)).append("\n")
                }
            logger.error(deprecationMessage)
            exitProcess(1)
        }
    }

    private fun Project.overrideAppPlatform() {
        configurations.configureEach {
            it.resolutionStrategy.eachDependency { dependency ->
                val requested = dependency.requested
                if (requested.group == "com.amazon.lastmile.app.platform") {
                    dependency.useTarget(
                        "com.amazon.lastmile.app.platform.k2:${requested.name}:${BuildConfig.APP_PLATFORM_VERSION}",
                    )
                }
            }
        }
    }

    private fun Project.isBrazilPackage(): Boolean {
        val hasBuildPeruGradle = File("${rootDir.absolutePath}/build-peru.gradle").exists()
        val configFile = File("${rootDir.absolutePath}/Config")
        val hasConfigFile = configFile.exists() && !configFile.isDirectory
        val hasPeruGradleProperties =
            File("${rootDir.absolutePath}/gradle-properties-peru").exists()
        val hasBrazilGradleProperties =
            File("${rootDir.absolutePath}/gradle-properties-brazil").exists()
        if (hasBuildPeruGradle) {
            logger.error(
                "CONFIGURATION: package has build-peru.gradle. Please, remove it and deprecate brazil configurations",
            )
        }
        if (hasConfigFile) {
            logger.error("CONFIGURATION: package has  Config. Please, remove it and deprecate brazil configurations")
        }
        if (hasPeruGradleProperties || hasBrazilGradleProperties) {
            logger.error(
                "CONFIGURATION: package has  HAS gradle-properties-brazil or gradle-properties-peru. " +
                    "Deprecate brazil or if already remove these configurations and move " +
                    "gradle-properties-peru contents into gradle.properties",
            )
            logger.error("Remove gradle.properties from .gitignore")
        }
        return hasBuildPeruGradle || hasConfigFile || hasPeruGradleProperties || hasBrazilGradleProperties
    }

    private fun isNowAfterEndOfMay22thUtc(): Boolean {
        val now = Instant.now()
        return now.isAfter(END_OF_MAY_22_UTC)
    }

    private fun Project.isPackageExempted(): Boolean {
        return EXEMPTED_PACAGES.any { exemptedPackage ->
            name.lowercase().startsWith(exemptedPackage) ||
                exemptedPackage.lowercase()
                    .startsWith(name.lowercase())
        }
    }

    companion object {
        private const val LENGTH_OF_MESSAGE = 80
        private val EXEMPTED_PACAGES =
            listOf(
                "ElCamino",
                "ElCaminoLib",
                "ElCaminoBase",
                "ElCaminoDeps",
                "LMDeps-Djinni",
                "GroundControlGuideCppMobileArtifacts",
                "MapsMetricsCatalog",
                "RDSMeridianTokenLibrary",
                "RDSMeridianTokensAndroid",
                "RDSTokenLibrary",
                "RDSTokensAndroid",
                "RabbitMobileMetricsAndroidSDK",
                "RabbitInstructionServiceClient-Kotlin",
                "RabbitInstructionSchemata",
                "MopacAndroidLogging",
                "RabbitMobileMetricsCatalog",
                "ReasonCodeMappingConfig",
                "ReasonCodeMappingLibJava",
            )

        private val END_OF_MAY_22_UTC =
            LocalDateTime.of(2024, 5, 22, 23, 59, 59).toInstant(ZoneOffset.UTC)
    }
}

object BuildPathUtil {

    private lateinit var internalProject: Project

    @JvmStatic
    fun buildPath(packageName: String): String {
        val brazilContextHome =
            if (System.getenv("BRAZIL_BUILD_HOME") != null) {
                "${System.getenv("BRAZIL_BUILD_HOME")}/bin/brazil-farm"
            } else {
                "brazil-farm"
            }
        val command = "$brazilContextHome --package $packageName"
        val process =
            ProcessBuilder("\\s".toRegex().split(command))
                .redirectOutput(ProcessBuilder.Redirect.PIPE)
                .redirectError(ProcessBuilder.Redirect.PIPE)
                .start()
        if (process.waitFor() != 0) {
            throw InterruptedException(
                "$this failed: ${
                    process.errorStream.bufferedReader().readText()
                }",
            )
        }
        return process.inputStream.bufferedReader().readText().trim()
    }

    // Task to resolve Brazil Farm package path
    abstract class ResolveBrazilFarmPathTask : DefaultTask() {
        @get:Input
        abstract val packageName: Property<String>

        @get:OutputFile
        abstract val outputFile: RegularFileProperty

        @get:Inject
        abstract val workerExecutor: WorkerExecutor

        @TaskAction
        fun resolveAndSavePath() {
            workerExecutor.noIsolation().submit(BrazilFarmPathWorker::class.java) { parameters ->
                parameters.packageName.set(packageName)
                parameters.outputFile.set(outputFile)
            }
        }

        abstract class BrazilFarmPathWorker : WorkAction<BrazilFarmPathParameters> {
            override fun execute() {
                val brazilContextHome = if (System.getenv("BRAZIL_BUILD_HOME") != null) {
                    "${System.getenv("BRAZIL_BUILD_HOME")}/bin/brazil-farm"
                } else {
                    "brazil-farm"
                }

                val process = ProcessBuilder(brazilContextHome, "--package", parameters.packageName.get())
                    .redirectOutput(ProcessBuilder.Redirect.PIPE)
                    .redirectError(ProcessBuilder.Redirect.PIPE)
                    .start()

                if (process.waitFor() != 0) {
                    throw GradleException(
                        "Brazil farm command failed: ${process.errorStream.bufferedReader().readText()}",
                    )
                }

                val path = process.inputStream.bufferedReader().readText().trim()
                parameters.outputFile.get().asFile.parentFile.mkdirs()
                parameters.outputFile.get().asFile.writeText(path)
            }
        }

        interface BrazilFarmPathParameters : WorkParameters {
            val packageName: Property<String>
            val outputFile: RegularFileProperty
        }
    }

    abstract class CopyBrazilPathFilesTask : DefaultTask() {
        @get:InputFile
        abstract val pathFile: RegularFileProperty

        @get:OutputDirectory
        abstract val outputDir: DirectoryProperty

        @get:Input
        abstract val subPath: Property<String>

        @get:Inject
        abstract val fs: FileSystemOperations

        @TaskAction
        fun copyFiles() {
            if (pathFile.get().asFile.exists()) {
                val resolvedPath = pathFile.get().asFile.readText().trim() + "/" + subPath.get()
                val outputDirFile = outputDir.get().asFile
                val srcDir = File(resolvedPath)

                if (srcDir.exists()) {
                    fs.copy { copySpec ->
                        copySpec.from(srcDir)
                        copySpec.into(outputDirFile)
                    }
                    logger.lifecycle("Copied files from $resolvedPath to $outputDirFile")
                } else {
                    logger.warn("Source directory $resolvedPath not found")
                    outputDirFile.mkdirs() // Create empty dir as fallback
                }
            }
        }
    }

    // Utility class to simplify configuration in build.gradle files
    @JvmStatic
    fun configureStateMachineAssets(
        packageName: String,
        subPath: String = "main",
    ): Provider<Directory> {
        val sanitizedName = packageName.replace('-', '_').replace('.', '_')

        // Create path resolution task
        val resolvePathTask = internalProject.tasks.register(
            "resolve${sanitizedName}Path",
            ResolveBrazilFarmPathTask::class.java,
        ) {
            it.packageName.set(packageName)
            it.outputFile.set(internalProject.layout.buildDirectory.file("generated/$sanitizedName-path.txt"))
        }

        // Create copy files task
        val copyFilesTask = internalProject.tasks.register(
            "copy${sanitizedName}Files",
            CopyBrazilPathFilesTask::class.java,
        ) {
            it.dependsOn(resolvePathTask)
            it.pathFile.set(resolvePathTask.flatMap { task -> task.outputFile })
            it.outputDir.set(internalProject.layout.buildDirectory.dir("generated/$sanitizedName-assets"))
            it.subPath.set(subPath)
        }

        // Configure task dependencies using finalizedBy
        internalProject.tasks.withType(ProcessAndroidResources::class.java).configureEach {
            it.dependsOn(copyFilesTask)
        }

        internalProject.tasks.withType(MergeSourceSetFolders::class.java).configureEach {
            it.dependsOn(copyFilesTask)
        }

        return copyFilesTask.flatMap { it.outputDir }
    }

    internal fun setTarget(target: Project) {
        internalProject = target
    }
}

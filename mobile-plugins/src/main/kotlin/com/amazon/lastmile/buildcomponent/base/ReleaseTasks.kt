package com.amazon.lastmile.buildcomponent.base

import com.amazon.lastmile.buildcomponent.constant.LMBCNamedDomainContainer
import org.gradle.api.Project

fun Project.setupReleaseTasks() {
    val releaseTaskNotDefined =
        project.tasks.findByName(LMBCNamedDomainContainer.RELEASE) == null
    if (releaseTaskNotDefined) {
        tasks.register(LMBCNamedDomainContainer.RELEASE).configure {
            it.dependsOn(
                LMBCNamedDomainContainer.BUILD,
                LMBCNamedDomainContainer.PUBLISH,
            )
        }
    }

    val defaultTasksNotDefined = project.defaultTasks.isEmpty()
    if (defaultTasksNotDefined) {
        project.defaultTasks(LMBCNamedDomainContainer.RELEASE)
    }
}

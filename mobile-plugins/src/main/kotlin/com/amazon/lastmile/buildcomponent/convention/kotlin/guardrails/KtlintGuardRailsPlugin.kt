package com.amazon.lastmile.buildcomponent.convention.kotlin.guardrails

import com.amazon.lastmile.buildcomponent.addLMGPSupport
import com.amazon.lastmile.buildcomponent.constant.LMBCConfiguration
import com.amazon.lastmile.buildcomponent.constant.LMBCNamedDomainContainer
import com.amazon.lastmile.buildcomponent.extension.LMBCMobileExtension.Companion.lmbc
import com.amazon.lastmile.buildcomponent.lmbcBuildDir
import com.amazon.lastmile.buildcomponent.properties.isLmgpEnabled
import org.gradle.api.Plugin
import org.gradle.api.Project
import org.gradle.api.artifacts.Configuration
import org.gradle.api.tasks.JavaExec

/**
 * Ktlint Plugin applied to Kotlin Libraries.
 */
class KtlintGuardRailsPlugin : Plugin<Project> {
    override fun apply(target: Project) {
        target.afterEvaluate {
            val ktlintVersion = target.lmbc().codeQuality.getKtlintVersion()
            if (ktlintVersion != LMBCConfiguration.KTLINT_1_0_0) {
                val ktlintConfig = target.configurations.create("ktlint")
                target.addLMGPSupport(ktlintConfig.name)
                target.addKtlintDependencies(ktlintConfig)
                val mainClassPath = "com.github.shyiko.ktlint.Main"

                target.configureKtlintCheck(ktlintConfig, mainClassPath, ktlintVersion)
                target.configureKtlintFormat(ktlintConfig, mainClassPath)

                target.tasks.getByName(LMBCNamedDomainContainer.BUILD) {
                    it.dependsOn("ktlintCheck")
                }

                target.tasks.findByName(LMBCNamedDomainContainer.PRE_COMMIT_CODE_CHECKS)?.dependsOn("ktlintCheck")
            }
        }
    }

    private fun Project.addKtlintDependencies(ktlintConfig: Configuration) {
        if (isLmgpEnabled) {
            addLMGPSupport(ktlintConfig.name)
            dependencies.add(ktlintConfig.name, "com.github.shyiko:ktlint")
        } else {
            addKtlintExplicitDependencies(ktlintConfig)
        }
    }

    private fun Project.addKtlintExplicitDependencies(ktlintConfig: Configuration) {
        dependencies.add(ktlintConfig.name, "com.github.shyiko:ktlint:0.31.0")
    }

    private fun Project.configureKtlintCheck(
        ktlintConfig: Configuration,
        mainClassPath: String,
        ktlintVersion: String,
    ) {
        tasks.register("ktlintCheck", JavaExec::class.java) { javaExec ->
            javaExec.classpath(ktlintConfig)
            javaExec.mainClass.set(mainClassPath)
            javaExec.group = "verification"
            javaExec.isIgnoreExitValue = false
            // Ktlint Filters - see https://pinterest.github.io/ktlint/0.49.1/install/cli/#globs for more info
            javaExec.args("**/**/*.kt*")
            // excluding generated files from the checks
            javaExec.args("!build/**")

            if (ktlintVersion == LMBCConfiguration.KTLINT_0_31_0) {
                // SAM Functions not supported in ktlint 0.31.0.
                // TODO -remove this list after migrating ktlint version to latest.
                javaExec.args("!src/**/OnSnapshotReadyCallback.kt")
                javaExec.args("!src/**/MapCompletionCallback.kt")
                javaExec.args("!src/**/OnMapClickListener.kt")
                javaExec.args("!src/**/OnMapLoadedCallback.kt")
                javaExec.args("!src/**/OnMapLongClickListener.kt")
                javaExec.args("!src/**/OnMapReadyCallback.kt")
                javaExec.args("!src/**/OnMarkerClickListener.kt")
                javaExec.args("!src/**/OnMyLocationButtonClickListener.kt")
                javaExec.args("!src/**/OnPoiTapListener.kt")
                javaExec.args("!src/**/IOnMapReadyCallback.kt")
                javaExec.args("!src/**/OnPoiTapListener.kt")
                javaExec.args("!src/**/OnViewportTrackingChangedListener.kt")
                javaExec.args("!src/**/OnWayfindingMapReadyCallback.kt")
            }

            javaExec.args("--reporter=plain")
            javaExec.args("--reporter=checkstyle,output=$lmbcBuildDir/ktlint/check-report.xml")
        }
    }

    private fun Project.configureKtlintFormat(
        ktlintConfig: Configuration,
        mainClassPath: String,
    ) {
        tasks.register("ktlintFormat", JavaExec::class.java) { javaExec ->
            javaExec.jvmArgs(
                "--add-opens",
                "java.base/java.lang=ALL-UNNAMED",
            ) // https://github.com/pinterest/ktlint/issues/1391#issuecomment-1090990781
            javaExec.classpath(ktlintConfig)
            javaExec.mainClass.set(mainClassPath)
            javaExec.group = "verification"
            javaExec.args("-F", "**/**/*.kt*")
        }
    }
}

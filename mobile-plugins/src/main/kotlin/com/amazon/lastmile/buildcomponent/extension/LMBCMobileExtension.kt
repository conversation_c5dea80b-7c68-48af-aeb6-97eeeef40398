package com.amazon.lastmile.buildcomponent.extension

import com.amazon.lastmile.app.platform.gradle.AppPlatformExtension
import com.amazon.lastmile.app.platform.gradle.isAppModule
import com.amazon.lastmile.app.platform.gradle.isUsingModuleStructure
import com.amazon.lastmile.buildcomponent.constant.LMBCNamedDomainContainer.LMBC_EXT_PROPERTY
import com.amazon.lastmile.buildcomponent.constant.LMBCPluginId
import com.amazon.lastmile.buildcomponent.feature.appPlatformExtension
import com.amazon.lastmile.buildcomponent.feature.enableTedp
import org.gradle.api.Action
import org.gradle.api.Project
import org.gradle.api.model.ObjectFactory
import org.gradle.api.provider.Property
import javax.inject.Inject

open class LMBCMobileExtension @Inject constructor(
    objectFactory: ObjectFactory,
    private val project: Project,
) {
    val codeQuality = objectFactory.newInstance(CodeQualityExtension::class.java, project)

    private val enableAppPlatform: Property<Boolean> = objectFactory.property(Boolean::class.java).convention(false)

    private val enableTedp: Property<Boolean> = objectFactory.property(Boolean::class.java).convention(false)

    fun codeQuality(action: Action<CodeQualityExtension>) {
        action.execute(codeQuality)
    }

    fun enableAppPlatform(enabled: Boolean) {
        enableAppPlatform.set(enabled)
        enableAppPlatform.disallowChanges()
        if (enabled) {
            project.plugins.apply(LMBCPluginId.APP_PLATFORM)
            project.appPlatformExtension.addPublicModuleDependencies(true)

            // Enable the module structure and the expected rules, if a project is using App Platform
            // and follows the naming convention. Exclude app modules for now and make it an explicit
            // opt-in for them.
            if (project.isUsingModuleStructure() && !project.isAppModule()) {
                project.appPlatformExtension.enableModuleStructure(project.isUsingModuleStructure())
            }
        }
    }

    fun enableAppPlatform(action: Action<AppPlatformExtension>) {
        enableAppPlatform(true)
        action.execute(project.appPlatformExtension)
    }

    fun enableTedp(enabled: Boolean) {
        enableTedp.set(enabled)
        enableTedp.disallowChanges()
        if (enabled) {
            project.enableTedp()
        }
    }

    companion object {
        fun Project.createExtension() {
            extensions.create(LMBC_EXT_PROPERTY, LMBCMobileExtension::class.java)
        }

        fun Project.lmbc(): LMBCMobileExtension {
            return extensions.getByType(LMBCMobileExtension::class.java)
        }
    }
}

package com.amazon.lastmile.buildcomponent.convention.android

import com.amazon.lastmile.buildcomponent.android
import com.amazon.lastmile.buildcomponent.constant.LMBCConfiguration
import com.amazon.lastmile.buildcomponent.constant.LMBCNamedDomainContainer
import com.amazon.lastmile.buildcomponent.libraryResourcePath
import com.amazon.lastmile.buildcomponent.lmbcResourcesPath
import org.gradle.api.Project
import org.gradle.api.tasks.Copy
import org.gradle.api.tasks.compile.JavaCompile
import org.gradle.api.tasks.testing.Test

fun Project.addDataBinding() {
    with(android) {
        buildFeatures {
            dataBinding {
                enable = true
            }
        }
    }
}

fun Project.setupProguard() {
    tasks.register(LMBCNamedDomainContainer.PROGUARD_ANDROID_SETUP, Copy::class.java) {
        it.from(file("${lmbcResourcesPath()}/proguard/proguard-android.txt"))
        it.into("${libraryResourcePath(this.name)}/conf/proguard")
        it.fileMode = LMBCConfiguration.COPY_FILE_PERMISSION
    }

    tasks.register(LMBCNamedDomainContainer.PROGUARD_RELEASE_SETUP, Copy::class.java) {
        it.from(file("${lmbcResourcesPath()}/proguard/proguard-release.txt"))
        it.into("${libraryResourcePath(this.name)}/conf/proguard")
        it.fileMode = LMBCConfiguration.COPY_FILE_PERMISSION
    }
}

fun Project.configureJavaCompile() {
    tasks.withType(JavaCompile::class.java).configureEach {
        it.options.isFork = true
    }
}

fun Project.configureTestParallelTests() {
    tasks.withType(Test::class.java).configureEach {
        it.maxParallelForks = (Runtime.getRuntime().availableProcessors() / 2).takeIf { it > 0 } ?: 1
        it.testLogging {
            it.events("SKIPPED", "FAILED")
        }
        it.setForkEvery(LastMileAndroidBasePlugin.MAX_TEST_CASES)
        it.maxHeapSize = "2048m"
        it.minHeapSize = "1024m"
    }
}

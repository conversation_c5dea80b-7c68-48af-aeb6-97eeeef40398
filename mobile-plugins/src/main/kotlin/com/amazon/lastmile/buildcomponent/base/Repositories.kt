package com.amazon.lastmile.buildcomponent.base

import org.gradle.api.Project
import org.gradle.api.publish.PublishingExtension

fun Project.setupWireRepositories() {
    extensions.getByType(PublishingExtension::class.java).apply {
        repositories.apply {
            val wirePackageRepositoryUrl = extensions.extraProperties["wirePackageRepositoryUrl"]!!
            maven {
                it.setUrl(wirePackageRepositoryUrl)
                it.isAllowInsecureProtocol = true
            }
        }
    }
}

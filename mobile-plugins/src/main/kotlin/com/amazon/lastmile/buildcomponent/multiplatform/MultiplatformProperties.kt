package com.amazon.lastmile.buildcomponent.multiplatform

import org.gradle.api.Project

val OS_NAME = System.getProperty("os.name").lowercase()
val OS_ARCH = System.getProperty("os.arch").lowercase()

enum class HostOs {
    LINUX,
    WINDOWS,
    MACOS,
}

val HOST_NAME =
    when {
        OS_NAME.startsWith("linux") -> HostOs.LINUX
        OS_NAME.startsWith("windows") -> HostOs.WINDOWS
        OS_NAME.startsWith("mac") -> HostOs.MACOS
        else -> error("Unknown os name `$OS_NAME`")
    }

fun Project.isX86(): Boolean {
    return OS_ARCH in listOf("i386", "x86_64", "amd64")
}

fun Project.isArm64(): <PERSON><PERSON><PERSON> {
    return OS_ARCH in listOf("arm", "arm64", "aarch64")
}

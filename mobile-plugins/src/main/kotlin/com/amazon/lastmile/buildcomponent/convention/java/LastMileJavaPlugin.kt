package com.amazon.lastmile.buildcomponent.convention.java

import com.amazon.lastmile.buildcomponent.addLMGPSupport
import com.amazon.lastmile.buildcomponent.addTestDependencies
import com.amazon.lastmile.buildcomponent.constant.LMBCConfiguration
import com.amazon.lastmile.buildcomponent.constant.LMBCNamedDomainContainer
import com.amazon.lastmile.buildcomponent.constant.LMBCPluginId
import com.amazon.lastmile.buildcomponent.convention.base.LastMileJVMBasePlugin
import com.amazon.lastmile.buildcomponent.convention.base.LastMilePeruRepositoriesPlugin
import com.amazon.lastmile.buildcomponent.kover.LastMileKoverPlugin
import com.amazon.lastmile.buildcomponent.setupPublication
import org.gradle.api.Plugin
import org.gradle.api.Project
import org.gradle.api.logging.LogLevel
import org.gradle.api.plugins.JavaPluginExtension
import org.gradle.api.tasks.testing.Test

/**
 * Gradle Plugin applied for Java libraries.
 */
class LastMileJavaPlugin : Plugin<Project> {
    override fun apply(target: Project) {
        target.logger.log(LogLevel.INFO, "Applying LastMileJavaPlugin...")
        target.applyPlugins()
        target.configureJavaVersion()
        target.addAnnotationProcessors()
        target.addTestDependencies()
        target.setupPublication()
        target.configureUnitTests()
    }

    private fun Project.applyPlugins() {
        plugins.apply(LMBCPluginId.JAVA_LIBRARY)
        plugins.apply(LastMileJVMBasePlugin::class.java)
        plugins.apply(LastMileJavaGuardRailsPlugin::class.java)
        plugins.apply(LastMilePeruRepositoriesPlugin::class.java)
        plugins.apply(LastMileKoverPlugin::class.java)
    }

    private fun Project.configureUnitTests() {
        tasks.withType(Test::class.java).configureEach {
            it.maxParallelForks = (Runtime.getRuntime().availableProcessors() / 2).takeIf { it > 0 } ?: 1
        }
    }

    private fun Project.addAnnotationProcessors() {
        addLMGPSupport(LMBCNamedDomainContainer.ANNOTATION_PROCESSOR)
        addLMGPSupport(LMBCNamedDomainContainer.TEST_ANNOTATION_PROCESSOR)
        addLMGPSupport(LMBCNamedDomainContainer.COMPILE_ONLY)
        addLMGPSupport(LMBCNamedDomainContainer.TEST_COMPILE_ONLY)
        dependencies.add(LMBCNamedDomainContainer.ANNOTATION_PROCESSOR, "org.projectlombok:lombok")
        dependencies.add(LMBCNamedDomainContainer.TEST_ANNOTATION_PROCESSOR, "org.projectlombok:lombok")
        dependencies.add(LMBCNamedDomainContainer.COMPILE_ONLY, "org.projectlombok:lombok")
        dependencies.add(LMBCNamedDomainContainer.TEST_COMPILE_ONLY, "org.projectlombok:lombok")
    }

    private fun Project.configureJavaVersion() {
        with(extensions.getByType(JavaPluginExtension::class.java)) {
            sourceCompatibility = LMBCConfiguration.JAVA_VERSION
            targetCompatibility = LMBCConfiguration.JAVA_VERSION
        }
    }
}

plugins {
    id("com.amazon.lastmile.lmbc.gradle.plugin")
}

gradlePlugin {
    plugins {
        create("lastMileAndroidProjectPlugin") {
            id = "com.amazon.lastmile.android.project"
            displayName = "LastMile Android Project"
            implementationClass = "com.amazon.lastmile.buildcomponent.convention.android.LastMileAndroidProjectPlugin"
            description = "Gradle Plugin applied for Android Top Level."
        }
        create("lastMileAndroidAppPlugin") {
            id = "com.amazon.lastmile.android.app"
            displayName = "LastMile Android App Plugin"
            implementationClass = "com.amazon.lastmile.buildcomponent.convention.android.LastMileAndroidAppPlugin"
            description = "Gradle Plugin applied for Android app module."
        }
        create("lastMileAndroidDynamicFeaturePlugin") {
            id = "com.amazon.lastmile.android.dynamic.feature"
            displayName = "LastMile Android Dynamic Feature Plugin"
            implementationClass =
                "com.amazon.lastmile.buildcomponent.convention.android.LastMileAndroidDynamicFeaturePlugin"
            description = "Gradle Plugin applied for Android dynamic feature module."
        }
        create("lastMileAndroidLibPlugin") {
            id = "com.amazon.lastmile.android.lib"
            displayName = "LastMile Android Lib Plugin"
            implementationClass = "com.amazon.lastmile.buildcomponent.convention.android.LastMileAndroidLibPlugin"
            description = "Gradle Plugin applied for Android library."
        }
        create("lastMileJavaLibPlugin") {
            id = "com.amazon.lastmile.java.lib"
            displayName = "LastMile Java Lib Plugin"
            implementationClass = "com.amazon.lastmile.buildcomponent.convention.java.LastMileJavaPlugin"
            description = "Gradle Plugin applied for Java library."
        }
        create("lastMileKotlinLibPlugin") {
            id = "com.amazon.lastmile.kotlin.lib"
            displayName = "LastMile Kotlin Lib Plugin"
            implementationClass = "com.amazon.lastmile.buildcomponent.convention.kotlin.LastMileKotlinPlugin"
            description = "Gradle Plugin applied for Kotlin library."
        }
        create("lastMilePantherPlugin") {
            id = "com.amazon.lastmile.feature.panther"
            displayName = "LastMile Android Panther Plugin"
            implementationClass = "com.amazon.lastmile.buildcomponent.convention.feature.LastMilePantherPlugin"
            description = "Gradle Plugin applied for Kotlin library."
        }
        create("lastMileAnnotationProcessor") {
            id = "com.amazon.lastmile.feature.annotationprocessor"
            displayName = "LastMile Android Annotation Processor Plugin"
            implementationClass =
                "com.amazon.lastmile.buildcomponent.convention.feature.LastMileAnnotationProcessorPlugin"
            description = "Gradle Plugin applied for Kotlin library."
        }
    }
}

tasks.jar {
    from(project(":configurations").sourceSets.main.get().resources)
}

buildConfig {
    buildConfigField("APP_PLATFORM_VERSION", libs.versions.app.platform.get())
    buildConfigField("KOTLIN_VERSION", libs.versions.kotlin.version.get())
}

dependencies {
    implementation(libs.spotbugs.gradle.plugin)
    implementation(libs.detekt)
    implementation(libs.kover)
    implementation(libs.ktlint)

    implementation(libs.android.app)
    implementation(libs.android.lib)
    implementation(libs.kotlin.gradle.plugin)
    /**
     * Temporary, this will again switch once we move the shared packages into using K2
     */
    implementation(libs.app.platform.gradle.plugin)
    // implementation(lastMileLibs.app.platform.gradle.plugin)
    implementation(lastMileLibs.tedp.gradle.plugin)

    implementation(libs.junit.jupiter)
    implementation(libs.panther.xml.handler)
    implementation(libs.panther.gradle.plugin)
    // Temporary. Need to verify why exports did not pick this up
    implementation(libs.google.guice)

    implementation(libs.vanniktech)

    testImplementation(libs.mockk)
}

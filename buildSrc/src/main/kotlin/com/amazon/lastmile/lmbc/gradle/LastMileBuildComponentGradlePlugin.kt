package com.amazon.lastmile.lmbc.gradle

import buildSrc.BuildConfig
import io.gitlab.arturbosch.detekt.extensions.DetektExtension
import org.gradle.api.JavaVersion
import org.gradle.api.Plugin
import org.gradle.api.Project
import org.gradle.api.plugins.JavaPlugin
import org.gradle.api.publish.PublishingExtension
import org.gradle.api.publish.maven.MavenPublication
import org.gradle.api.tasks.testing.Test
import org.gradle.jvm.toolchain.JavaLanguageVersion
import org.jetbrains.kotlin.gradle.dsl.kotlinExtension
import org.jlleitschuh.gradle.ktlint.KtlintExtension

class LastMileBuildComponentGradlePlugin : Plugin<Project> {
    override fun apply(target: Project) {
        with(target) {
            applyPlugins()
            addPrecommitChecks()
            addReleaseTask()
            configureJavaCompileOptions()
            configureTest()
            configureKtlint()
            configureDetekt()
            configurePublishing()
            overrideAppPlatform()
            group = GROUP_ID
            version = VERSION
        }
    }

    private fun Project.applyPlugins() {
        plugins.apply("java-gradle-plugin")
        plugins.apply("com.vanniktech.maven.publish")
        plugins.apply("org.jetbrains.kotlin.jvm")
        plugins.apply("org.jetbrains.kotlinx.kover")
        plugins.apply("io.gitlab.arturbosch.detekt")
        plugins.apply("com.github.gmazzo.buildconfig")
    }

    private fun Project.addPrecommitChecks() {
        tasks.getByName("build").dependsOn("ktlintCheck", "detekt")
    }

    private fun Project.addReleaseTask() {
        tasks.register("release") {
            it.dependsOn("build", "publish")
        }
        defaultTasks("release")
    }

    private fun Project.configureJavaCompileOptions() {
        with(kotlinExtension) {
            jvmToolchain(JavaLanguageVersion.of(JavaVersion.VERSION_11.majorVersion).asInt())
        }
    }

    private fun Project.configureTest() {
        dependencies.add("testImplementation", "io.mockk:mockk:${MOCKK_VERSION}")
        dependencies.add(
            JavaPlugin.TEST_IMPLEMENTATION_CONFIGURATION_NAME,
            "org.junit.jupiter:junit-jupiter:${JUPITER_VERSION}",
        )
        dependencies.add(
            JavaPlugin.TEST_IMPLEMENTATION_CONFIGURATION_NAME,
            "org.junit.jupiter:junit-jupiter-engine:${JUPITER_VERSION}",
        )
        tasks.withType(Test::class.java) {
            it.useJUnitPlatform()
        }
    }

    private fun Project.configureKtlint() {
        plugins.apply("org.jlleitschuh.gradle.ktlint")
        extensions.getByType(KtlintExtension::class.java).filter {
            it.exclude { element ->
                element.file.path.contains("generated/")
            }
        }
    }

    private fun Project.configureDetekt() {
        with(extensions.getByType(DetektExtension::class.java)) {
            config = files("$rootDir/configurations/src/main/resources/configurations/detekt/detekt.yml")
        }
    }

    private fun Project.configurePublishing() {
        with(extensions.getByType(PublishingExtension::class.java)) {
            publications { pluginContainer ->
                publications.forEach { publication ->
                    pluginContainer.create("pluginMaven${publication.name}", MavenPublication::class.java)
                }
            }

            repositories {
                val wirePackageRepositoryUrl = project.extensions.extraProperties["wirePackageRepositoryUrl"]!!
                it.maven { repository ->
                    repository.setUrl(wirePackageRepositoryUrl)
                    repository.isAllowInsecureProtocol = true
                }
            }
        }
    }

    private fun Project.overrideAppPlatform() {
        configurations.configureEach {
            it.resolutionStrategy.eachDependency { dependency ->
                val requested = dependency.requested
                if (requested.group == "com.amazon.lastmile.app.platform") {
                    dependency.useTarget(
                        "com.amazon.lastmile.app.platform.k2:${requested.name}:${BuildConfig.APP_PLATFORM_VERSION}",
                    )
                }
            }
        }
    }

    companion object {
        private const val GROUP_ID = "com.amazon.lastmile.buildcomponent"
        private const val VERSION = "1.0.0"

        // Testing
        const val JUPITER_VERSION = "5.9.2"
        const val MOCKK_VERSION = "1.13.4"
    }
}

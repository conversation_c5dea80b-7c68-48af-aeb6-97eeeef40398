repositories {
    val wireReadRepositoryUrl: java.net.URI by project
    maven {
        url = wireReadRepositoryUrl
        isAllowInsecureProtocol = true
    }
}

@Suppress("DSL_SCOPE_VIOLATION")
plugins {
    id("java-gradle-plugin")
    alias(libs.plugins.kotlin.jvm)
    alias(libs.plugins.ktlint)
    alias(libs.plugins.detekt)
    alias(libs.plugins.gradle.buildconfig)
}

ktlint {
    filter {
        exclude { element ->
            element.file.path.contains("generated/")
        }
    }
}

buildConfig {
    buildConfigField("APP_PLATFORM_VERSION", libs.versions.app.platform.get())
}

gradlePlugin {
    plugins {
        create("lmbcGradlePlugin") {
            id = "com.amazon.lastmile.lmbc.gradle.plugin"
            displayName = "LMBC Gradle Plugin"
            implementationClass = "com.amazon.lastmile.lmbc.gradle.LastMileBuildComponentGradlePlugin"
            description = "LMBC Gradle Plugin"
        }
    }
}

dependencies {
    implementation(libs.kover)
    implementation(libs.ktlint)
    implementation(libs.detekt)
    implementation(libs.vanniktech)
    implementation(libs.kotlin.gradle.plugin)
    implementation(libs.gradle.build.config)
}
